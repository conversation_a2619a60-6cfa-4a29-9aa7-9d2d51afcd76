"""
Supabase Session管理器使用示例

展示所有优化功能：
- 🔒 安全：加密存储
- ⚡ 性能：异步操作
- 🎯 功能：事件系统、多用户
- 🏗️ 架构：状态管理
- 👥 用户体验：智能刷新
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from backend.app.services.supabase_session_manager import (
    get_session_manager, 
    SessionEvent
)


async def demo_basic_usage():
    """演示基本使用方法"""
    print("🚀 Supabase Session管理器基本使用")
    print("=" * 40)
    
    # 获取session管理器
    session_mgr = get_session_manager()
    
    # 必须先初始化
    await session_mgr.initialize()
    
    # 检查登录状态
    if session_mgr.is_logged_in():
        print("✅ 已有有效登录状态")
        user = session_mgr.get_current_user()
        print(f"   当前用户: {user['email']}")
        
        # 获取session详细信息
        info = session_mgr.get_session_info()
        print(f"   登录时间: {info['login_time']}")
        print(f"   过期时间: {info['expires_at']}")
        print(f"   距离过期: {info['time_until_expiry']}秒")
        
        return True
    else:
        print("未登录，执行登录...")
        result = await session_mgr.login(
            email="<EMAIL>",
            password="heygo01!",
            remember_me=True,
            user_metadata={"source": "demo"}
        )
        
        if result["success"]:
            print("✅ 登录成功!")
            print(f"   用户: {result['user']['email']}")
            return True
        else:
            print(f"❌ 登录失败: {result['message']}")
            return False


async def demo_event_system():
    """演示事件系统"""
    print("\n🎭 事件系统演示")
    print("=" * 30)
    
    session_mgr = get_session_manager()
    
    # 事件计数器
    event_count = {"login": 0, "logout": 0, "error": 0}
    
    # 注册事件处理器
    async def on_login_success(event, data):
        event_count["login"] += 1
        print(f"🎉 登录成功事件 #{event_count['login']}: {data['user']['email']}")
    
    async def on_logout(event, data):
        event_count["logout"] += 1
        print(f"👋 登出事件 #{event_count['logout']}: {data['email']}")
    
    def on_error(event, data):
        event_count["error"] += 1
        print(f"❌ 错误事件 #{event_count['error']}: {data['error']}")
    
    # 注册事件监听器
    session_mgr.on(SessionEvent.LOGIN_SUCCESS, on_login_success)
    session_mgr.on(SessionEvent.LOGOUT, on_logout)
    session_mgr.on(SessionEvent.ERROR_OCCURRED, on_error)
    
    print("✅ 事件处理器已注册")
    print(f"   当前事件统计: {event_count}")
    
    return session_mgr


async def demo_security_features():
    """演示安全功能"""
    print("\n🔒 安全功能演示")
    print("=" * 30)
    
    session_mgr = get_session_manager()
    
    print("1️⃣ 加密存储:")
    print(f"   Session文件: {session_mgr.session_file}")
    print("   ✅ 所有敏感数据都经过Fernet加密存储")
    print("   ✅ 基于文件路径生成唯一加密密钥")
    
    print("\n2️⃣ Token安全:")
    if session_mgr.is_logged_in():
        token = session_mgr.get_access_token()
        print(f"   访问令牌: {token[:20]}... (已截断显示)")
        print("   ✅ Token获取时自动更新活动时间")
        print("   ✅ 支持自动刷新防止过期")
    else:
        print("   ⚠️ 未登录，无法获取token")
    
    print("\n3️⃣ 状态保护:")
    print(f"   当前状态: {session_mgr._state.value}")
    print("   ✅ 使用状态机防止非法状态转换")
    print("   ✅ 异步锁防止竞态条件")


async def demo_smart_refresh():
    """演示智能刷新功能"""
    print("\n🔄 智能刷新功能演示")
    print("=" * 40)
    
    session_mgr = get_session_manager()
    
    if not session_mgr.is_logged_in():
        print("⚠️ 用户未登录，跳过刷新演示")
        return
    
    print("1️⃣ 当前session状态:")
    info = session_mgr.get_session_info()
    print(f"   有效: {info['is_valid']}")
    print(f"   距离过期: {info['time_until_expiry']}秒")
    print(f"   最后活动: {info['last_activity']}")
    
    print("\n2️⃣ 自动刷新机制:")
    print(f"   自动刷新: {'启用' if session_mgr.auto_refresh else '禁用'}")
    print(f"   刷新缓冲时间: {session_mgr.refresh_buffer}秒")
    print("   ℹ️ 系统会在token过期前自动刷新")
    
    print("\n3️⃣ 手动刷新测试:")
    refresh_result = await session_mgr.refresh_session()
    if refresh_result["success"]:
        print("   ✅ 手动刷新成功")
        print(f"   新过期时间: {refresh_result['expires_at']}")
    else:
        print(f"   ❌ 手动刷新失败: {refresh_result['message']}")


async def demo_performance_features():
    """演示性能优化功能"""
    print("\n⚡ 性能优化演示")
    print("=" * 30)
    
    session_mgr = get_session_manager()
    
    print("1️⃣ 异步操作:")
    print("   ✅ 所有I/O操作都是异步的")
    print("   ✅ 使用异步锁防止竞态条件")
    print("   ✅ 非阻塞的文件加密/解密")
    
    print("\n2️⃣ 智能缓存:")
    print(f"   当前缓存的sessions: {len(session_mgr._sessions)}")
    print("   ✅ 内存中缓存活跃session")
    print("   ✅ 自动清理过期session")
    
    print("\n3️⃣ 后台任务:")
    if session_mgr._refresh_task and not session_mgr._refresh_task.done():
        print("   ✅ 自动刷新任务正在运行")
        print("   ✅ 每分钟检查一次token状态")
    else:
        print("   ⚠️ 自动刷新任务未运行")
    
    print("\n4️⃣ 资源管理:")
    print("   ✅ 自动清理过期数据")
    print("   ✅ 优雅的任务取消机制")


async def demo_error_handling():
    """演示错误处理"""
    print("\n🛡️ 错误处理演示")
    print("=" * 30)
    
    session_mgr = get_session_manager()
    
    # 注册错误事件处理器
    error_count = 0
    
    def error_handler(event, data):
        nonlocal error_count
        error_count += 1
        print(f"   捕获到错误 #{error_count}: {data['error']}")
        print(f"   错误上下文: {data.get('context', 'unknown')}")
    
    session_mgr.on(SessionEvent.ERROR_OCCURRED, error_handler)
    
    print("1️⃣ 错误事件处理:")
    print("   ✅ 统一的错误事件系统")
    print("   ✅ 详细的错误上下文信息")
    print("   ✅ 自动错误恢复机制")
    
    # 模拟一个错误（尝试用错误密码登录）
    print("\n2️⃣ 模拟登录错误:")
    result = await session_mgr.login("<EMAIL>", "wrong_password")
    if not result["success"]:
        print(f"   预期的登录失败: {result['message']}")
    
    print(f"\n   总共捕获错误: {error_count} 个")


async def demo_cleanup():
    """演示资源清理"""
    print("\n🧹 资源清理演示")
    print("=" * 30)
    
    session_mgr = get_session_manager()
    
    print("1️⃣ 登出清理:")
    if session_mgr.is_logged_in():
        result = await session_mgr.logout()
        if result["success"]:
            print("   ✅ 登出成功，资源已清理")
            print("   ✅ 本地session已删除")
            print("   ✅ 加密文件已清理")
        else:
            print(f"   ❌ 登出失败: {result['message']}")
    else:
        print("   ℹ️ 用户未登录，无需清理")
    
    print("\n2️⃣ 任务清理:")
    if session_mgr._refresh_task:
        session_mgr._refresh_task.cancel()
        print("   ✅ 后台刷新任务已取消")
    
    print("\n3️⃣ 文件清理:")
    if session_mgr.session_file.exists():
        print(f"   Session文件存在: {session_mgr.session_file}")
        print("   ℹ️ 文件在登出时会自动清理")
    else:
        print("   ✅ Session文件已清理")


async def main():
    """主演示函数"""
    print("🚀 Supabase Session管理器完整演示")
    print("=" * 50)
    
    try:
        # 1. 基本使用演示
        login_success = await demo_basic_usage()
        
        if login_success:
            # 2. 事件系统演示
            await demo_event_system()
            
            # 3. 安全功能演示
            await demo_security_features()
            
            # 4. 智能刷新演示
            await demo_smart_refresh()
            
            # 5. 性能功能演示
            await demo_performance_features()
        
        # 6. 错误处理演示
        await demo_error_handling()
        
        # 7. 资源清理演示
        await demo_cleanup()
        
        print("\n🎉 所有演示完成!")
        
        print("\n💡 功能总结:")
        print("✅ 安全性: 加密存储、防token泄露")
        print("✅ 性能: 异步操作、智能缓存")
        print("✅ 功能: 事件系统、自动刷新")
        print("✅ 架构: 依赖注入、状态机、错误处理")
        print("✅ 用户体验: 智能刷新、详细状态信息")
        
    except Exception as e:
        print(f"❌ 演示过程中出错: {e}")


if __name__ == "__main__":
    # 需要先安装cryptography库
    try:
        import cryptography
    except ImportError:
        print("❌ 需要安装cryptography库:")
        print("   uv add cryptography")
        exit(1)
    
    asyncio.run(main())
