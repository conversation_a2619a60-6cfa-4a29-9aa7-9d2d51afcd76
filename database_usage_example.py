"""
数据库操作使用示例

展示如何使用DatabaseManager进行各种数据库操作
包括用户级操作（RLS）、管理员操作和事务
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from backend.services.database_manager import get_database_manager
from backend.services.session_manager import get_session_manager


async def demo_user_operations():
    """演示用户级操作（RLS保护）"""
    
    print("👤 用户级操作演示（RLS保护）")
    print("=" * 40)
    
    db_manager = get_database_manager()
    session_manager = get_session_manager()
    
    # 首先确保用户已登录
    if not session_manager.is_logged_in():
        print("用户未登录，执行登录...")
        login_result = await session_manager.login("<EMAIL>", "heygo01!", remember_me=True)
        if not login_result["success"]:
            print(f"❌ 登录失败: {login_result['message']}")
            return
        print("✅ 登录成功")
    
    try:
        # 1. 用户查询操作
        print("\n1️⃣ 用户查询操作...")
        # 假设有一个user_profiles表
        profiles = await db_manager.user_select(
            table="user_profiles",
            columns="id, email, full_name, created_at"
        )
        print(f"   查询到 {len(profiles)} 条用户资料记录")
        
        # 2. 用户插入操作
        print("\n2️⃣ 用户插入操作...")
        new_profile = {
            "full_name": "测试用户",
            "bio": "这是一个测试用户资料"
        }
        inserted = await db_manager.user_insert("user_profiles", new_profile)
        print(f"   插入成功: {len(inserted)} 条记录")
        
        # 3. 用户更新操作
        print("\n3️⃣ 用户更新操作...")
        if inserted:
            profile_id = inserted[0]["id"]
            updated = await db_manager.user_update(
                table="user_profiles",
                data={"bio": "更新后的用户简介"},
                filters={"id": profile_id}
            )
            print(f"   更新成功: {len(updated)} 条记录")
        
        # 4. 用户删除操作
        print("\n4️⃣ 用户删除操作...")
        if inserted:
            profile_id = inserted[0]["id"]
            deleted = await db_manager.user_delete(
                table="user_profiles",
                filters={"id": profile_id}
            )
            print(f"   删除成功: {len(deleted)} 条记录")
            
    except Exception as e:
        print(f"❌ 用户操作出错: {e}")


async def demo_admin_operations():
    """演示管理员级操作（绕过RLS）"""
    
    print("\n🔧 管理员级操作演示（绕过RLS）")
    print("=" * 40)
    
    db_manager = get_database_manager()
    
    try:
        # 1. 管理员查询 - 跨用户数据
        print("\n1️⃣ 管理员查询（跨用户数据）...")
        all_users = await db_manager.admin_select(
            "SELECT id, email, created_at FROM auth.users LIMIT 10"
        )
        print(f"   查询到 {len(all_users)} 个用户")
        for user in all_users[:3]:  # 显示前3个
            print(f"   - {user['email']} (创建于: {user['created_at']})")
        
        # 2. 管理员统计查询
        print("\n2️⃣ 管理员统计查询...")
        stats = await db_manager.admin_select(
            "SELECT COUNT(*) as total_users FROM auth.users"
        )
        print(f"   总用户数: {stats[0]['total_users']}")
        
        # 3. 管理员执行操作
        print("\n3️⃣ 管理员执行操作...")
        # 创建一个测试表（如果不存在）
        create_table_sql = """
        CREATE TABLE IF NOT EXISTS test_admin_table (
            id SERIAL PRIMARY KEY,
            name TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT NOW()
        )
        """
        result = await db_manager.admin_execute(create_table_sql)
        print(f"   表创建结果: {result}")
        
        # 插入测试数据
        insert_sql = "INSERT INTO test_admin_table (name) VALUES ($1) RETURNING *"
        result = await db_manager.admin_execute(insert_sql, ["管理员测试数据"])
        print(f"   数据插入结果: {result}")
        
    except Exception as e:
        print(f"❌ 管理员操作出错: {e}")


async def demo_transaction_operations():
    """演示事务操作"""
    
    print("\n💳 事务操作演示")
    print("=" * 30)
    
    db_manager = get_database_manager()
    
    try:
        # 1. 管理员事务
        print("\n1️⃣ 管理员事务操作...")
        async with db_manager.transaction(user_context=False) as tx:
            # 在事务中执行多个操作
            await tx.execute(
                "INSERT INTO test_admin_table (name) VALUES ($1)",
                "事务测试数据1"
            )
            await tx.execute(
                "INSERT INTO test_admin_table (name) VALUES ($1)",
                "事务测试数据2"
            )
            
            # 查询插入的数据
            result = await tx.fetch(
                "SELECT * FROM test_admin_table WHERE name LIKE '事务测试%'"
            )
            print(f"   事务中插入了 {len(result)} 条记录")
        
        print("✅ 管理员事务提交成功")
        
        # 2. 用户事务（如果用户已登录）
        session_manager = get_session_manager()
        if session_manager.is_logged_in():
            print("\n2️⃣ 用户事务操作...")
            try:
                async with db_manager.transaction(user_context=True) as tx:
                    # 在用户上下文中执行事务
                    await tx.execute(
                        "INSERT INTO user_profiles (full_name, bio) VALUES ($1, $2)",
                        "事务用户", "通过事务创建的用户"
                    )
                    
                    # 查询用户数据
                    user_data = await tx.fetch(
                        "SELECT * FROM user_profiles WHERE full_name = $1",
                        "事务用户"
                    )
                    print(f"   用户事务中创建了 {len(user_data)} 条记录")
                
                print("✅ 用户事务提交成功")
                
            except Exception as e:
                print(f"⚠️ 用户事务失败（可能是权限或表不存在）: {e}")
        else:
            print("⚠️ 用户未登录，跳过用户事务演示")
        
        # 3. 事务回滚演示
        print("\n3️⃣ 事务回滚演示...")
        try:
            async with db_manager.transaction(user_context=False) as tx:
                await tx.execute(
                    "INSERT INTO test_admin_table (name) VALUES ($1)",
                    "将被回滚的数据"
                )
                
                # 故意抛出异常来触发回滚
                raise Exception("故意触发回滚")
                
        except Exception as e:
            if "故意触发回滚" in str(e):
                print("✅ 事务回滚成功（数据未被插入）")
            else:
                print(f"❌ 意外错误: {e}")
        
    except Exception as e:
        print(f"❌ 事务操作出错: {e}")


async def demo_mixed_operations():
    """演示混合操作场景"""
    
    print("\n🔄 混合操作场景演示")
    print("=" * 30)
    
    db_manager = get_database_manager()
    session_manager = get_session_manager()
    
    try:
        # 场景：用户注册后的数据初始化
        print("\n📝 场景：用户注册后的数据初始化")
        
        if session_manager.is_logged_in():
            current_user = session_manager.get_current_user()
            user_id = current_user["id"]
            user_email = current_user["email"]
            
            # 使用事务确保数据一致性
            async with db_manager.transaction(user_context=False) as tx:
                # 1. 创建用户资料
                await tx.execute(
                    """
                    INSERT INTO user_profiles (user_id, email, full_name, created_at)
                    VALUES ($1, $2, $3, NOW())
                    ON CONFLICT (user_id) DO NOTHING
                    """,
                    user_id, user_email, "默认用户名"
                )
                
                # 2. 创建用户设置
                await tx.execute(
                    """
                    INSERT INTO user_settings (user_id, theme, language, notifications)
                    VALUES ($1, $2, $3, $4)
                    ON CONFLICT (user_id) DO NOTHING
                    """,
                    user_id, "light", "zh-CN", True
                )
                
                # 3. 记录用户活动
                await tx.execute(
                    """
                    INSERT INTO user_activities (user_id, activity_type, description)
                    VALUES ($1, $2, $3)
                    """,
                    user_id, "profile_created", "用户资料初始化完成"
                )
            
            print("✅ 用户数据初始化完成")
            
            # 然后用户可以查询自己的数据（受RLS保护）
            user_profile = await db_manager.user_select(
                "user_profiles",
                "full_name, email, created_at",
                {"user_id": user_id}
            )
            print(f"   用户资料: {user_profile}")
            
        else:
            print("⚠️ 用户未登录，跳过混合操作演示")
            
    except Exception as e:
        print(f"❌ 混合操作出错: {e}")


async def main():
    """主函数"""
    
    print("🚀 Supabase数据库操作完整演示")
    print("=" * 50)
    
    print("选择演示内容:")
    print("1. 用户级操作（RLS保护）")
    print("2. 管理员级操作（绕过RLS）")
    print("3. 事务操作")
    print("4. 混合操作场景")
    print("5. 运行所有演示")
    
    choice = input("\n请输入选择 (1-5): ").strip()
    
    try:
        if choice == "1":
            await demo_user_operations()
        elif choice == "2":
            await demo_admin_operations()
        elif choice == "3":
            await demo_transaction_operations()
        elif choice == "4":
            await demo_mixed_operations()
        elif choice == "5":
            await demo_user_operations()
            await demo_admin_operations()
            await demo_transaction_operations()
            await demo_mixed_operations()
        else:
            print("❌ 无效选择")
            return
        
        print("\n🎉 演示完成!")
        
    except Exception as e:
        print(f"❌ 演示过程中出错: {e}")
    
    finally:
        # 清理资源
        db_manager = get_database_manager()
        await db_manager.close_pool()
        print("\n🧹 资源清理完成")


if __name__ == "__main__":
    asyncio.run(main())
