"""
Supabase数据库操作管理器

提供完整的数据库操作功能，支持：
1. 用户级操作（RLS保护）
2. 管理员级操作
3. 事务支持
4. 跨用户数据操作
"""

from typing import Optional, Dict, Any, List, Union
from contextlib import asynccontextmanager
from loguru import logger
import asyncpg
from supabase import Client

from backend.app.services.supabase_client import get_supabase_client
from backend.app.services.supabase_auth_service import get_auth_service
from backend.app.services.supabase_session_manager import get_session_manager
from backend.app.config.env import supabase_settings


class DatabaseManager:
    """数据库操作管理器"""
    
    def __init__(self):
        """初始化数据库管理器"""
        self.supabase_client = get_supabase_client()
        self.session_manager = get_session_manager()
        self._db_pool: Optional[asyncpg.Pool] = None
        
        # 构建数据库连接字符串
        self.db_url = (
            f"postgresql://{supabase_settings.supabase_db_username}:"
            f"{supabase_settings.supabase_db_password}@"
            f"{supabase_settings.supabase_db_host}:"
            f"{supabase_settings.supabase_db_port}/"
            f"{supabase_settings.supabase_db_database}"
        )
        
        logger.info("Database manager initialized")
    
    async def _ensure_pool(self):
        """确保数据库连接池已创建"""
        if self._db_pool is None:
            try:
                self._db_pool = await asyncpg.create_pool(
                    self.db_url,
                    min_size=5,
                    max_size=20,
                    command_timeout=60
                )
                logger.info("Database connection pool created")
            except Exception as e:
                logger.error(f"Failed to create database pool: {e}")
                raise
    
    async def close_pool(self):
        """关闭数据库连接池"""
        if self._db_pool:
            await self._db_pool.close()
            self._db_pool = None
            logger.info("Database connection pool closed")
    
    # ==================== 用户级操作（RLS保护） ====================
    
    async def user_select(self, table: str, columns: str = "*", filters: Dict[str, Any] = None) -> List[Dict]:
        """
        用户级查询操作（受RLS保护）
        
        Args:
            table: 表名
            columns: 查询列，默认为所有列
            filters: 过滤条件
            
        Returns:
            查询结果列表
        """
        try:
            # 确保用户已登录
            if not self.session_manager.is_logged_in():
                raise Exception("User not logged in")
            
            # 确保session有效
            if not await self.session_manager.ensure_valid_session():
                raise Exception("Invalid user session")
            
            # 使用用户的access_token进行查询
            client = self._get_user_client()
            query = client.table(table).select(columns)
            
            # 应用过滤条件
            if filters:
                for key, value in filters.items():
                    query = query.eq(key, value)
            
            result = query.execute()
            logger.info(f"User query executed on table {table}")
            return result.data
            
        except Exception as e:
            logger.error(f"User select error: {e}")
            raise
    
    async def user_insert(self, table: str, data: Union[Dict[str, Any], List[Dict[str, Any]]]) -> List[Dict]:
        """
        用户级插入操作（受RLS保护）
        
        Args:
            table: 表名
            data: 插入数据
            
        Returns:
            插入的记录
        """
        try:
            if not self.session_manager.is_logged_in():
                raise Exception("User not logged in")
            
            if not await self.session_manager.ensure_valid_session():
                raise Exception("Invalid user session")
            
            client = self._get_user_client()
            result = client.table(table).insert(data).execute()
            logger.info(f"User insert executed on table {table}")
            return result.data
            
        except Exception as e:
            logger.error(f"User insert error: {e}")
            raise
    
    async def user_update(self, table: str, data: Dict[str, Any], filters: Dict[str, Any]) -> List[Dict]:
        """
        用户级更新操作（受RLS保护）
        
        Args:
            table: 表名
            data: 更新数据
            filters: 过滤条件
            
        Returns:
            更新的记录
        """
        try:
            if not self.session_manager.is_logged_in():
                raise Exception("User not logged in")
            
            if not await self.session_manager.ensure_valid_session():
                raise Exception("Invalid user session")
            
            client = self._get_user_client()
            query = client.table(table).update(data)
            
            # 应用过滤条件
            for key, value in filters.items():
                query = query.eq(key, value)
            
            result = query.execute()
            logger.info(f"User update executed on table {table}")
            return result.data
            
        except Exception as e:
            logger.error(f"User update error: {e}")
            raise
    
    async def user_delete(self, table: str, filters: Dict[str, Any]) -> List[Dict]:
        """
        用户级删除操作（受RLS保护）
        
        Args:
            table: 表名
            filters: 过滤条件
            
        Returns:
            删除的记录
        """
        try:
            if not self.session_manager.is_logged_in():
                raise Exception("User not logged in")
            
            if not await self.session_manager.ensure_valid_session():
                raise Exception("Invalid user session")
            
            client = self._get_user_client()
            query = client.table(table).delete()
            
            # 应用过滤条件
            for key, value in filters.items():
                query = query.eq(key, value)
            
            result = query.execute()
            logger.info(f"User delete executed on table {table}")
            return result.data
            
        except Exception as e:
            logger.error(f"User delete error: {e}")
            raise
    
    # ==================== 管理员级操作 ====================
    
    async def admin_select(self, query: str, params: List = None) -> List[Dict]:
        """
        管理员级查询操作（绕过RLS）
        
        Args:
            query: SQL查询语句
            params: 查询参数
            
        Returns:
            查询结果
        """
        await self._ensure_pool()
        
        try:
            async with self._db_pool.acquire() as conn:
                # 设置为service_role绕过RLS
                await conn.execute("SET LOCAL role TO service_role")
                
                if params:
                    result = await conn.fetch(query, *params)
                else:
                    result = await conn.fetch(query)
                
                # 转换为字典列表
                return [dict(row) for row in result]
                
        except Exception as e:
            logger.error(f"Admin select error: {e}")
            raise
    
    async def admin_execute(self, query: str, params: List = None) -> str:
        """
        管理员级执行操作（绕过RLS）
        
        Args:
            query: SQL语句
            params: 参数
            
        Returns:
            执行状态
        """
        await self._ensure_pool()
        
        try:
            async with self._db_pool.acquire() as conn:
                await conn.execute("SET LOCAL role TO service_role")
                
                if params:
                    result = await conn.execute(query, *params)
                else:
                    result = await conn.execute(query)
                
                logger.info(f"Admin execute completed: {result}")
                return result
                
        except Exception as e:
            logger.error(f"Admin execute error: {e}")
            raise
    
    # ==================== 事务支持 ====================
    
    @asynccontextmanager
    async def transaction(self, user_context: bool = False):
        """
        数据库事务上下文管理器
        
        Args:
            user_context: 是否在用户上下文中执行（应用RLS）
            
        Usage:
            async with db_manager.transaction() as tx:
                await tx.execute("INSERT INTO ...")
                await tx.execute("UPDATE ...")
        """
        await self._ensure_pool()
        
        async with self._db_pool.acquire() as conn:
            async with conn.transaction():
                if not user_context:
                    # 管理员上下文，绕过RLS
                    await conn.execute("SET LOCAL role TO service_role")
                else:
                    # 用户上下文，需要设置用户JWT
                    if not self.session_manager.is_logged_in():
                        raise Exception("User not logged in for transaction")
                    
                    access_token = self.session_manager.get_access_token()
                    await conn.execute(f"SELECT set_config('request.jwt.claims', '{access_token}', true)")
                
                # 创建事务对象
                tx = TransactionContext(conn)
                try:
                    yield tx
                    logger.info("Transaction completed successfully")
                except Exception as e:
                    logger.error(f"Transaction failed: {e}")
                    raise
    
    def _get_user_client(self) -> Client:
        """获取带用户认证的Supabase客户端"""
        # 这里需要设置用户的access_token到客户端
        # 注意：supabase-py客户端的token设置方式可能需要调整
        client = self.supabase_client.get_client()
        
        # 设置用户token（具体实现可能需要根据supabase-py版本调整）
        access_token = self.session_manager.get_access_token()
        if access_token:
            # 这里的实现可能需要根据实际的supabase-py API调整
            client.auth.set_session(access_token, self.session_manager._current_session.get("refresh_token"))
        
        return client


class TransactionContext:
    """事务上下文"""
    
    def __init__(self, connection):
        self.conn = connection
    
    async def execute(self, query: str, *params) -> str:
        """执行SQL语句"""
        return await self.conn.execute(query, *params)
    
    async def fetch(self, query: str, *params) -> List[Dict]:
        """查询数据"""
        result = await self.conn.fetch(query, *params)
        return [dict(row) for row in result]
    
    async def fetchrow(self, query: str, *params) -> Optional[Dict]:
        """查询单行数据"""
        result = await self.conn.fetchrow(query, *params)
        return dict(result) if result else None


# 创建全局数据库管理器实例
database_manager = DatabaseManager()


def get_database_manager() -> DatabaseManager:
    """
    获取数据库管理器实例
    
    Returns:
        DatabaseManager: 数据库管理器实例
    """
    return database_manager
