import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from backend.app.services.supabase_auth_service import get_auth_service
from backend.app.services.supabase_session_manager_service import get_session_manager
from backend.app.services.database_manager import get_database_manager
import asyncio

async def test_basic_auth():
    """测试基本认证功能"""
    print("\n=== 测试基本认证功能 ===")

    auth_service = get_auth_service()

    # 1. 尝试登录
    print("1️⃣ 尝试登录...")
    login_result = await auth_service.sign_in_with_email("<EMAIL>", "heygo01!")

    if login_result["success"]:
        print("✅ 登录成功")
        print(f"   用户邮箱: {login_result['user']['email']}")
        print(f"   用户ID: {login_result['user']['id']}")
        print(f"   访问令牌: {login_result['access_token'][:30]}...")

        # 2. 获取当前用户
        print("\n2️⃣ 获取当前用户...")
        user_result = await auth_service.get_current_user()
        if user_result["success"]:
            print("✅ 获取用户信息成功")
            print(f"   当前用户: {user_result['user']['email']}")
        else:
            print(f"❌ 获取用户信息失败: {user_result['message']}")

        return True
    else:
        print(f"❌ 登录失败: {login_result['message']}")
        return False

async def test_session_manager():
    """测试Session管理器"""
    print("\n=== 测试Session管理器 ===")

    session_mgr = get_session_manager()

    # 1. 检查登录状态
    print("1️⃣ 检查登录状态...")
    if session_mgr.is_logged_in():
        print("✅ 已有有效登录状态")
        user = session_mgr.get_current_user()
        print(f"   当前用户: {user['email']}")
        return True
    else:
        print("未登录，执行登录...")
        response = await session_mgr.login("<EMAIL>", "heygo01!", remember_me=True)
        if response["success"]:
            print("✅ 登录成功")
            print(f"   用户: {response['user']['email']}")
            return True
        else:
            print(f"❌ 登录失败: {response['message']}")
            return False

async def test_database_operations():
    """测试数据库操作"""
    print("\n=== 测试数据库操作 ===")

    db_mgr = get_database_manager()

    try:
        # 1. 测试管理员查询
        print("1️⃣ 测试管理员查询...")
        users = await db_mgr.admin_select(
            "SELECT id, email, created_at FROM auth.users LIMIT 3"
        )
        print(f"   查询到 {len(users)} 个用户")
        for user in users:
            print(f"   - {user['email']} (ID: {user['id'][:8]}...)")

        # 2. 测试创建表
        print("\n2️⃣ 测试创建表...")
        create_sql = """
        CREATE TABLE IF NOT EXISTS python_test_table (
            id SERIAL PRIMARY KEY,
            name TEXT NOT NULL,
            description TEXT,
            created_at TIMESTAMP DEFAULT NOW()
        )
        """
        result = await db_mgr.admin_execute(create_sql)
        print(f"   表创建结果: {result}")

        # 3. 测试插入数据
        print("\n3️⃣ 测试插入数据...")
        insert_sql = "INSERT INTO python_test_table (name, description) VALUES ($1, $2) RETURNING *"
        result = await db_mgr.admin_execute(
            insert_sql,
            ["Python测试", f"测试时间: {asyncio.get_event_loop().time()}"]
        )
        print(f"   数据插入结果: {result}")

        # 4. 测试查询数据
        print("\n4️⃣ 测试查询数据...")
        select_result = await db_mgr.admin_select(
            "SELECT * FROM python_test_table ORDER BY created_at DESC LIMIT 5"
        )
        print(f"   查询到 {len(select_result)} 条记录")
        for record in select_result:
            print(f"   - {record['name']}: {record['description']}")

        return True

    except Exception as e:
        print(f"❌ 数据库操作出错: {e}")
        return False

async def test_transaction():
    """测试事务功能"""
    print("\n=== 测试事务功能 ===")

    db_mgr = get_database_manager()

    try:
        # 1. 成功的事务
        print("1️⃣ 测试成功事务...")
        async with db_mgr.transaction(user_context=False) as tx:
            await tx.execute(
                "INSERT INTO python_test_table (name, description) VALUES ($1, $2)",
                "事务测试1", "第一条事务数据"
            )
            await tx.execute(
                "INSERT INTO python_test_table (name, description) VALUES ($1, $2)",
                "事务测试2", "第二条事务数据"
            )

            result = await tx.fetch(
                "SELECT * FROM python_test_table WHERE name LIKE '事务测试%'"
            )
            print(f"   事务中插入了 {len(result)} 条记录")

        print("   ✅ 事务提交成功")

        # 2. 失败的事务（测试回滚）
        print("\n2️⃣ 测试事务回滚...")
        try:
            async with db_mgr.transaction(user_context=False) as tx:
                await tx.execute(
                    "INSERT INTO python_test_table (name, description) VALUES ($1, $2)",
                    "将被回滚", "这条数据不会被保存"
                )
                # 故意触发错误
                raise Exception("测试回滚")
        except Exception as e:
            if "测试回滚" in str(e):
                print("   ✅ 事务回滚成功")
            else:
                raise

        return True

    except Exception as e:
        print(f"❌ 事务测试出错: {e}")
        return False

async def main():
    """主测试函数"""
    print("🚀 Supabase完整功能测试")
    print("=" * 50)

    try:
        # 1. 测试基本认证
        auth_ok = await test_basic_auth()
        if not auth_ok:
            print("❌ 基本认证测试失败")
            return

        # 2. 测试Session管理器
        session_ok = await test_session_manager()
        if not session_ok:
            print("❌ Session管理器测试失败")
            return

        # 3. 测试数据库操作
        db_ok = await test_database_operations()
        if not db_ok:
            print("❌ 数据库操作测试失败")

        # 4. 测试事务
        tx_ok = await test_transaction()
        if not tx_ok:
            print("❌ 事务测试失败")

        print("\n🎉 测试完成!")

    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")

    finally:
        # 清理资源
        print("\n🧹 清理资源...")
        try:
            db_mgr = get_database_manager()
            await db_mgr.close_pool()
            print("数据库连接池已关闭")
        except:
            pass

if __name__ == "__main__":
    asyncio.run(main())