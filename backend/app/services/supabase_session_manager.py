"""
Supabase Session管理器

全面优化版本，包含：
- 🔒 安全：加密存储、防token泄露
- ⚡ 性能：异步操作、智能缓存
- 🎯 功能：多用户支持、事件系统、自动刷新
- 🏗️ 架构：依赖注入、状态机、错误处理
- 👥 用户体验：智能刷新、状态同步
"""

import json
import time
import asyncio
import hashlib
import base64
from enum import Enum
from typing import Optional, Dict, Any, List, Callable
from pathlib import Path
from cryptography.fernet import Fernet
from loguru import logger

from backend.app.services.supabase_client import get_auth_service, get_supabase_client
from backend.app.api.v1.schemas import (
    SessionData,
    SessionState,
    SessionEvent,
    SessionManagerError,
    SessionExpiredError,
    SessionNotFoundError,
    SessionValidationError
)


# ==================== Session管理器 ====================


class SupabaseSessionManager:
    """Supabase Session管理器"""
    
    def __init__(
        self, 
        session_file: str = "supabase_session.json",
        auth_service=None,
        encryption_key: Optional[str] = None,
        auto_refresh: bool = True,
        multi_user: bool = False,
        refresh_buffer: int = 600  # 提前10分钟刷新
    ):
        """
        初始化Supabase Session管理器
        
        Args:
            session_file: session存储文件路径
            auth_service: 认证服务实例（依赖注入）
            encryption_key: 加密密钥
            auto_refresh: 是否自动刷新token
            multi_user: 是否支持多用户
            refresh_buffer: 刷新缓冲时间（秒）
        """
        self.session_file = Path(session_file)
        self.auth_service = auth_service or get_auth_service()
        self.supabase_client = get_supabase_client()
        self.auto_refresh = auto_refresh
        self.multi_user = multi_user
        self.refresh_buffer = refresh_buffer
        
        # 状态管理
        self._state = SessionState.LOGGED_OUT
        self._current_session: Optional[SessionData] = None
        self._sessions: Dict[str, SessionData] = {}  # 多用户支持
        
        # 事件系统
        self._event_handlers: Dict[SessionEvent, List[Callable]] = {}
        
        # 加密设置
        self._setup_encryption(encryption_key)
        
        # 异步锁和任务
        self._lock = asyncio.Lock()
        self._refresh_task: Optional[asyncio.Task] = None
        self._initialized = False
        
        logger.info("Supabase SessionManager initialized")
    
    # ==================== 初始化和加密 ====================
    
    def _setup_encryption(self, encryption_key: Optional[str] = None):
        """设置加密"""
        if encryption_key:
            self._fernet = Fernet(encryption_key.encode())
        else:
            # 生成基于机器的密钥
            machine_id = hashlib.sha256(str(self.session_file.absolute()).encode()).digest()
            key = base64.urlsafe_b64encode(machine_id[:32])
            self._fernet = Fernet(key)
    
    async def initialize(self):
        """异步初始化（必须调用）"""
        if self._initialized:
            return
        
        await self._load_sessions()
        if self.auto_refresh:
            self._start_refresh_task()
        
        self._initialized = True
        logger.info("Supabase SessionManager initialization completed")
    
    def _start_refresh_task(self):
        """启动自动刷新任务"""
        if self._refresh_task and not self._refresh_task.done():
            self._refresh_task.cancel()
        
        self._refresh_task = asyncio.create_task(self._auto_refresh_loop())
    
    async def _auto_refresh_loop(self):
        """智能自动刷新循环"""
        while True:
            try:
                await asyncio.sleep(60)  # 每分钟检查一次
                
                if self._current_session and self._current_session.is_expired(self.refresh_buffer):
                    logger.info("Auto-refreshing session...")
                    await self.refresh_session()
                    
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Auto-refresh error: {e}")
                await asyncio.sleep(300)  # 出错后等待5分钟再试
    
    async def _load_sessions(self) -> None:
        """异步加载sessions"""
        try:
            if not self.session_file.exists():
                return
            
            with open(self.session_file, 'rb') as f:
                encrypted_data = f.read()
            
            if not encrypted_data:
                return
            
            # 解密数据
            try:
                decrypted_data = self._fernet.decrypt(encrypted_data)
                session_data = json.loads(decrypted_data.decode())
            except Exception as e:
                logger.error(f"Failed to decrypt session data: {e}")
                self.session_file.unlink(missing_ok=True)
                return
            
            # 加载sessions
            if self.multi_user:
                # 多用户模式
                for user_id, data in session_data.items():
                    try:
                        # 使用Pydantic的model_validate方法
                        session = SessionData.model_validate(data)
                        if not session.is_expired():
                            self._sessions[user_id] = session
                            if not self._current_session:
                                self._current_session = session
                                self._state = SessionState.LOGGED_IN
                                logger.info(f"Loaded valid session for user: {session.email}")
                    except Exception as e:
                        logger.error(f"Error loading session for user {user_id}: {e}")
            else:
                # 单用户模式
                try:
                    # 使用Pydantic的model_validate方法
                    session = SessionData.model_validate(session_data)
                    if not session.is_expired():
                        self._current_session = session
                        self._sessions[session.user_id] = session
                        self._state = SessionState.LOGGED_IN
                        logger.info(f"Loaded valid session for user: {session.email}")
                    else:
                        logger.info("Session expired, removing file")
                        self.session_file.unlink(missing_ok=True)
                except Exception as e:
                    logger.error(f"Error loading session: {e}")
                    self.session_file.unlink(missing_ok=True)
                    
        except Exception as e:
            logger.error(f"Error loading sessions: {e}")
            self.session_file.unlink(missing_ok=True)
    
    async def _save_sessions(self) -> None:
        """异步保存sessions"""
        try:
            async with self._lock:
                if self.multi_user:
                    data_to_save = {
                        user_id: session.model_dump()
                        for user_id, session in self._sessions.items()
                    }
                else:
                    if self._current_session:
                        data_to_save = self._current_session.model_dump()
                    else:
                        self.session_file.unlink(missing_ok=True)
                        return
                
                # 加密并保存
                json_data = json.dumps(data_to_save, indent=2, ensure_ascii=False)
                encrypted_data = self._fernet.encrypt(json_data.encode())
                
                with open(self.session_file, 'wb') as f:
                    f.write(encrypted_data)
                
                logger.debug("Sessions saved successfully")
                
        except Exception as e:
            logger.error(f"Error saving sessions: {e}")
    
    # ==================== 事件系统 ====================
    
    def on(self, event: SessionEvent, handler: Callable):
        """注册事件处理器"""
        if event not in self._event_handlers:
            self._event_handlers[event] = []
        self._event_handlers[event].append(handler)
    
    def off(self, event: SessionEvent, handler: Callable):
        """移除事件处理器"""
        if event in self._event_handlers:
            try:
                self._event_handlers[event].remove(handler)
            except ValueError:
                pass
    
    async def _emit_event(self, event: SessionEvent, data: Dict[str, Any] = None):
        """触发事件"""
        if event in self._event_handlers:
            for handler in self._event_handlers[event]:
                try:
                    if asyncio.iscoroutinefunction(handler):
                        await handler(event, data or {})
                    else:
                        handler(event, data or {})
                except Exception as e:
                    logger.error(f"Event handler error for {event}: {e}")

    # ==================== 核心登录登出方法 ====================

    async def login(
        self,
        email: str,
        password: str,
        remember_me: bool = True,
        user_metadata: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """
        用户登录

        Args:
            email: 用户邮箱
            password: 用户密码
            remember_me: 是否记住登录状态
            user_metadata: 额外的用户元数据

        Returns:
            登录结果
        """
        if not self._initialized:
            await self.initialize()

        async with self._lock:
            try:
                self._state = SessionState.LOGGING_IN
                await self._emit_event(SessionEvent.LOGIN_START, {"email": email})

                # 如果已有session，先清理
                if self._current_session:
                    await self._cleanup_current_session()

                # 执行登录
                result = await self.auth_service.sign_in_with_email(email, password)

                if result["success"] and result.get("session"):
                    # 创建session数据
                    session_data = SessionData(
                        user_id=result["user"]["id"],
                        email=result["user"]["email"],
                        access_token=result["access_token"],
                        refresh_token=result["refresh_token"],
                        expires_at=int(result["session"]["expires_at"]),
                        login_time=int(time.time()),
                        last_activity=int(time.time()),
                        user_metadata=user_metadata or {},
                        session_metadata={
                            "login_method": "email_password",
                            "remember_me": remember_me,
                            "user_agent": "python_client"
                        }
                    )

                    # 设置当前session
                    self._current_session = session_data
                    self._sessions[session_data.user_id] = session_data
                    self._state = SessionState.LOGGED_IN

                    # 保存session（如果选择记住）
                    if remember_me:
                        await self._save_sessions()

                    # 启动自动刷新
                    if self.auto_refresh:
                        self._start_refresh_task()

                    logger.info(f"User logged in successfully: {email}")

                    success_data = {
                        "user": result["user"],
                        "session_expires_at": result["session"]["expires_at"],
                        "login_time": session_data.login_time
                    }

                    await self._emit_event(SessionEvent.LOGIN_SUCCESS, success_data)

                    return {
                        "success": True,
                        "message": "Login successful",
                        **success_data
                    }
                else:
                    self._state = SessionState.LOGGED_OUT
                    await self._emit_event(SessionEvent.LOGIN_FAILED, {"email": email, "reason": result.get("message")})
                    return result

            except Exception as e:
                self._state = SessionState.ERROR
                error_msg = str(e)
                logger.error(f"Login error: {error_msg}")

                await self._emit_event(SessionEvent.ERROR_OCCURRED, {"error": error_msg, "context": "login"})

                return {
                    "success": False,
                    "message": f"Login failed: {error_msg}"
                }

    async def logout(self, user_id: Optional[str] = None) -> Dict[str, Any]:
        """
        用户登出

        Args:
            user_id: 指定用户ID（多用户模式）

        Returns:
            登出结果
        """
        async with self._lock:
            try:
                # 确定要登出的session
                if user_id and self.multi_user:
                    session_to_logout = self._sessions.get(user_id)
                    if not session_to_logout:
                        return {"success": False, "message": "User session not found"}
                else:
                    session_to_logout = self._current_session
                    if not session_to_logout:
                        return {"success": False, "message": "No active session"}

                # 调用Supabase登出API
                result = await self.auth_service.sign_out()

                # 清理本地session
                if user_id and self.multi_user:
                    self._sessions.pop(user_id, None)
                    if self._current_session and self._current_session.user_id == user_id:
                        self._current_session = next(iter(self._sessions.values()), None)
                else:
                    self._current_session = None
                    self._sessions.clear()

                # 更新状态
                self._state = SessionState.LOGGED_OUT if not self._current_session else SessionState.LOGGED_IN

                # 停止自动刷新任务
                if not self._current_session and self._refresh_task:
                    self._refresh_task.cancel()

                # 保存更新后的sessions
                await self._save_sessions()

                logger.info(f"User logged out successfully: {session_to_logout.email}")

                logout_data = {
                    "user_id": session_to_logout.user_id,
                    "email": session_to_logout.email
                }

                await self._emit_event(SessionEvent.LOGOUT, logout_data)

                return {
                    "success": True,
                    "message": "Logged out successfully"
                }

            except Exception as e:
                error_msg = str(e)
                logger.error(f"Logout error: {error_msg}")

                await self._emit_event(SessionEvent.ERROR_OCCURRED, {"error": error_msg, "context": "logout"})

                return {
                    "success": False,
                    "message": f"Logout failed: {error_msg}"
                }

    async def refresh_session(self) -> Dict[str, Any]:
        """刷新session"""
        if not self._current_session:
            return {"success": False, "message": "No active session to refresh"}

        async with self._lock:
            try:
                self._state = SessionState.REFRESHING
                await self._emit_event(SessionEvent.REFRESH_START)

                refresh_token = self._current_session.refresh_token
                result = await self.auth_service.refresh_session(refresh_token)

                if result["success"]:
                    # 更新session数据
                    self._current_session.access_token = result["access_token"]
                    self._current_session.refresh_token = result["refresh_token"]
                    self._current_session.expires_at = int(result["session"]["expires_at"])
                    self._current_session.update_activity()

                    # 更新sessions字典
                    self._sessions[self._current_session.user_id] = self._current_session

                    # 保存更新后的session
                    await self._save_sessions()

                    self._state = SessionState.LOGGED_IN
                    await self._emit_event(SessionEvent.REFRESH_SUCCESS)

                    logger.info("Session refreshed successfully")
                    return {
                        "success": True,
                        "message": "Session refreshed",
                        "expires_at": result["session"]["expires_at"]
                    }
                else:
                    # 刷新失败，清理session
                    await self.logout()
                    self._state = SessionState.EXPIRED
                    await self._emit_event(SessionEvent.REFRESH_FAILED)
                    return result

            except Exception as e:
                error_msg = str(e)
                logger.error(f"Session refresh error: {error_msg}")
                await self.logout()
                self._state = SessionState.ERROR
                await self._emit_event(SessionEvent.ERROR_OCCURRED, {"error": error_msg, "context": "refresh"})
                return {"success": False, "message": f"Refresh failed: {error_msg}"}

    async def ensure_valid_session(self) -> bool:
        """确保session有效，如果即将过期则自动刷新"""
        try:
            if not self._current_session:
                return False

            if self._current_session.is_expired(self.refresh_buffer):
                logger.info("Session expiring soon, attempting refresh...")
                refresh_result = await self.refresh_session()
                return refresh_result["success"]

            return True

        except Exception as e:
            logger.error(f"Error ensuring valid session: {e}")
            return False

    async def _cleanup_current_session(self):
        """清理当前session"""
        if self._current_session:
            try:
                await self.auth_service.sign_out()
            except Exception as e:
                logger.warning(f"Error during session cleanup: {e}")

            self._current_session = None

    # ==================== 状态查询方法 ====================

    def is_logged_in(self, user_id: Optional[str] = None) -> bool:
        """检查是否已登录"""
        if user_id and self.multi_user:
            session = self._sessions.get(user_id)
            return session is not None and not session.is_expired()

        return (
            self._current_session is not None and
            not self._current_session.is_expired() and
            self._state == SessionState.LOGGED_IN
        )

    def get_current_user(self, user_id: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """获取当前用户信息"""
        if user_id and self.multi_user:
            session = self._sessions.get(user_id)
        else:
            session = self._current_session

        if session and not session.is_expired():
            return {
                "id": session.user_id,
                "email": session.email,
                "metadata": session.user_metadata
            }
        return None

    def get_access_token(self, user_id: Optional[str] = None) -> Optional[str]:
        """获取访问令牌"""
        if user_id and self.multi_user:
            session = self._sessions.get(user_id)
        else:
            session = self._current_session

        if session and not session.is_expired():
            session.update_activity()  # 更新活动时间
            return session.access_token
        return None

    def get_session_info(self, user_id: Optional[str] = None) -> Dict[str, Any]:
        """获取session详细信息"""
        if user_id and self.multi_user:
            session = self._sessions.get(user_id)
        else:
            session = self._current_session

        if not session:
            return {
                "logged_in": False,
                "state": self._state.value,
                "message": "No active session"
            }

        return {
            "logged_in": not session.is_expired(),
            "state": self._state.value,
            "user_email": session.email,
            "user_id": session.user_id,
            "login_time": time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(session.login_time)),
            "expires_at": time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(session.expires_at)),
            "last_activity": time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(session.last_activity)),
            "is_valid": not session.is_expired(),
            "time_until_expiry": max(0, session.expires_at - int(time.time())),
            "session_metadata": session.session_metadata
        }


# 创建全局Supabase session管理器实例
supabase_session_manager = SupabaseSessionManager()


def get_session_manager() -> SupabaseSessionManager:
    """
    获取Supabase session管理器实例

    Returns:
        SupabaseSessionManager: Supabase session管理器实例
    """
    return supabase_session_manager
