"""
Session相关的Pydantic Schemas

包含所有session管理相关的数据模型、请求/响应模型和异常类
"""

import time
from enum import Enum
from typing import Optional, Dict, Any, List
from pydantic import BaseModel, Field, ConfigDict, field_validator


# ==================== 枚举类 ====================

class SessionState(str, Enum):
    """Session状态枚举"""
    LOGGED_OUT = "logged_out"
    LOGGING_IN = "logging_in"
    LOGGED_IN = "logged_in"
    REFRESHING = "refreshing"
    EXPIRED = "expired"
    ERROR = "error"


class SessionEvent(str, Enum):
    """Session事件枚举"""
    LOGIN_START = "login_start"
    LOGIN_SUCCESS = "login_success"
    LOGIN_FAILED = "login_failed"
    LOGOUT = "logout"
    REFRESH_START = "refresh_start"
    REFRESH_SUCCESS = "refresh_success"
    REFRESH_FAILED = "refresh_failed"
    SESSION_EXPIRED = "session_expired"
    ERROR_OCCURRED = "error_occurred"


# ==================== 核心数据模型 ====================

class SessionData(BaseModel):
    """Session数据结构 - 使用Pydantic 2"""
    
    model_config = ConfigDict(
        # 允许额外字段
        extra='allow',
        # 验证赋值
        validate_assignment=True,
        # 使用枚举值
        use_enum_values=True,
        # 序列化时排除None值
        exclude_none=True,
        # JSON schema生成
        json_schema_extra={
            "example": {
                "user_id": "123e4567-e89b-12d3-a456-426614174000",
                "email": "<EMAIL>",
                "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
                "refresh_token": "refresh_token_example_123456",
                "expires_at": 1735689600,
                "login_time": 1703153600,
                "last_activity": 1703153600,
                "user_metadata": {"source": "api"},
                "session_metadata": {"device": "web"}
            }
        }
    )
    
    user_id: str = Field(..., description="用户ID", min_length=1)
    email: str = Field(..., description="用户邮箱", pattern=r'^[^@]+@[^@]+\.[^@]+$')
    access_token: str = Field(..., description="访问令牌", min_length=10)
    refresh_token: str = Field(..., description="刷新令牌", min_length=10)
    expires_at: int = Field(..., description="过期时间戳", gt=0)
    login_time: int = Field(..., description="登录时间戳", gt=0)
    last_activity: int = Field(..., description="最后活动时间戳", gt=0)
    user_metadata: Dict[str, Any] = Field(default_factory=dict, description="用户元数据")
    session_metadata: Dict[str, Any] = Field(default_factory=dict, description="会话元数据")
    
    @field_validator('expires_at', 'login_time', 'last_activity')
    @classmethod
    def validate_timestamps(cls, v: int) -> int:
        """验证时间戳"""
        if v <= 0:
            raise ValueError('Timestamp must be positive')
        # 检查时间戳是否合理（不能是未来太远的时间）
        max_future = int(time.time()) + (365 * 24 * 60 * 60)  # 一年后
        if v > max_future:
            raise ValueError('Timestamp cannot be more than 1 year in the future')
        return v
    
    @field_validator('email')
    @classmethod
    def validate_email_format(cls, v: str) -> str:
        """验证邮箱格式"""
        if '@' not in v or '.' not in v.split('@')[-1]:
            raise ValueError('Invalid email format')
        return v.lower().strip()
    
    @field_validator('access_token', 'refresh_token')
    @classmethod
    def validate_tokens(cls, v: str) -> str:
        """验证令牌格式"""
        if not v or len(v) < 10:
            raise ValueError('Token must be at least 10 characters long')
        return v.strip()
    
    def is_expired(self, buffer_seconds: int = 300) -> bool:
        """
        检查是否过期
        
        Args:
            buffer_seconds: 缓冲时间（秒），默认提前5分钟
            
        Returns:
            是否过期
        """
        return time.time() > (self.expires_at - buffer_seconds)
    
    def update_activity(self) -> None:
        """更新最后活动时间"""
        self.last_activity = int(time.time())
    
    def time_until_expiry(self) -> int:
        """
        获取距离过期的时间（秒）
        
        Returns:
            距离过期的秒数，如果已过期返回0
        """
        return max(0, self.expires_at - int(time.time()))
    
    def is_recently_active(self, threshold_minutes: int = 30) -> bool:
        """
        检查是否最近活跃
        
        Args:
            threshold_minutes: 活跃阈值（分钟）
            
        Returns:
            是否最近活跃
        """
        threshold_seconds = threshold_minutes * 60
        return (int(time.time()) - self.last_activity) <= threshold_seconds
    
    def get_session_duration(self) -> int:
        """
        获取会话持续时间（秒）
        
        Returns:
            会话持续时间
        """
        return int(time.time()) - self.login_time
    
    def to_safe_dict(self) -> Dict[str, Any]:
        """
        转换为安全的字典（隐藏敏感信息）
        
        Returns:
            不包含敏感信息的字典
        """
        data = self.model_dump()
        # 隐藏敏感信息
        data['access_token'] = f"{data['access_token'][:10]}..."
        data['refresh_token'] = f"{data['refresh_token'][:10]}..."
        return data


# ==================== 异常模型 ====================

class SessionValidationErrorDetail(BaseModel):
    """Session验证错误详情"""
    
    field: str = Field(..., description="出错的字段")
    error: str = Field(..., description="错误信息")
    value: Any = Field(None, description="错误的值")
    
    def __str__(self) -> str:
        return f"Field '{self.field}': {self.error} (value: {self.value})"


class SessionManagerError(Exception):
    """Session管理器异常基类"""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(message)
        self.message = message
        self.details = details or {}


class SessionExpiredError(SessionManagerError):
    """Session过期异常"""
    
    def __init__(self, session_data: Optional[SessionData] = None):
        message = "Session has expired"
        details = {}
        if session_data:
            details = {
                "user_id": session_data.user_id,
                "email": session_data.email,
                "expired_at": session_data.expires_at,
                "current_time": int(time.time())
            }
        super().__init__(message, details)


class SessionNotFoundError(SessionManagerError):
    """Session不存在异常"""
    
    def __init__(self, user_id: Optional[str] = None):
        message = f"Session not found{f' for user {user_id}' if user_id else ''}"
        details = {"user_id": user_id} if user_id else {}
        super().__init__(message, details)


class SessionValidationError(SessionManagerError):
    """Session验证异常"""
    
    def __init__(self, validation_errors: List[Dict[str, Any]]):
        message = f"Session validation failed with {len(validation_errors)} errors"
        details = {"validation_errors": validation_errors}
        super().__init__(message, details)


# ==================== API请求/响应模型 ====================

class LoginRequest(BaseModel):
    """登录请求模型"""
    
    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "email": "<EMAIL>",
                "password": "password123",
                "remember_me": True,
                "user_metadata": {"source": "web"}
            }
        }
    )
    
    email: str = Field(..., description="用户邮箱", pattern=r'^[^@]+@[^@]+\.[^@]+$')
    password: str = Field(..., description="用户密码", min_length=6)
    remember_me: bool = Field(default=True, description="是否记住登录状态")
    user_metadata: Dict[str, Any] = Field(default_factory=dict, description="用户元数据")


class UserInfoResponse(BaseModel):
    """用户信息响应模型"""
    
    id: str = Field(..., description="用户ID")
    email: str = Field(..., description="用户邮箱")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="用户元数据")


class LoginResponse(BaseModel):
    """登录响应模型"""
    
    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="响应消息")
    user: Optional[UserInfoResponse] = Field(None, description="用户信息")
    session_expires_at: Optional[int] = Field(None, description="Session过期时间")
    login_time: Optional[int] = Field(None, description="登录时间")


class LogoutResponse(BaseModel):
    """登出响应模型"""
    
    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="响应消息")


class RefreshTokenRequest(BaseModel):
    """刷新令牌请求模型"""
    
    refresh_token: str = Field(..., description="刷新令牌", min_length=10)


class RefreshTokenResponse(BaseModel):
    """刷新令牌响应模型"""
    
    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="响应消息")
    expires_at: Optional[int] = Field(None, description="新的过期时间")


class SessionInfoResponse(BaseModel):
    """Session信息响应模型"""
    
    logged_in: bool = Field(..., description="是否已登录")
    state: SessionState = Field(..., description="Session状态")
    user_email: Optional[str] = Field(None, description="用户邮箱")
    user_id: Optional[str] = Field(None, description="用户ID")
    login_time: Optional[str] = Field(None, description="登录时间")
    expires_at: Optional[str] = Field(None, description="过期时间")
    last_activity: Optional[str] = Field(None, description="最后活动时间")
    is_valid: bool = Field(..., description="是否有效")
    time_until_expiry: int = Field(..., description="距离过期时间（秒）")
    session_metadata: Dict[str, Any] = Field(default_factory=dict, description="Session元数据")


# ==================== 数据库相关模型 ====================

class DatabaseQueryRequest(BaseModel):
    """数据库查询请求模型"""
    
    table: str = Field(..., description="表名", min_length=1)
    columns: str = Field(default="*", description="查询列")
    filters: Dict[str, Any] = Field(default_factory=dict, description="过滤条件")
    limit: Optional[int] = Field(default=None, description="限制条数", gt=0, le=1000)


class DatabaseInsertRequest(BaseModel):
    """数据库插入请求模型"""
    
    table: str = Field(..., description="表名", min_length=1)
    data: Dict[str, Any] = Field(..., description="插入数据")


class DatabaseUpdateRequest(BaseModel):
    """数据库更新请求模型"""
    
    table: str = Field(..., description="表名", min_length=1)
    data: Dict[str, Any] = Field(..., description="更新数据")
    filters: Dict[str, Any] = Field(..., description="过滤条件")


class DatabaseResponse(BaseModel):
    """数据库操作响应模型"""
    
    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="响应消息")
    data: List[Dict[str, Any]] = Field(default_factory=list, description="返回数据")
    count: int = Field(default=0, description="影响行数")
