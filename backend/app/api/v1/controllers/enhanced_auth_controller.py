"""
增强的认证控制器

使用Pydantic schemas进行请求/响应验证
"""

from fastapi import APIRouter, HTTPException, Depends
from fastapi.responses import JSONResponse
from loguru import logger

from backend.app.services.supabase_session_manager import get_session_manager
from backend.app.api.v1.schemas import (
    LoginRequest,
    LoginResponse,
    LogoutResponse,
    RefreshTokenRequest,
    RefreshTokenResponse,
    SessionInfoResponse,
    UserInfoResponse,
    SessionManagerError,
    SessionExpiredError,
    SessionNotFoundError
)

router = APIRouter(prefix="/auth", tags=["Enhanced Authentication"])


def get_session_manager_dependency():
    """依赖注入：获取session管理器"""
    return get_session_manager()


@router.post("/login", response_model=LoginResponse, summary="用户登录")
async def login(
    request: LoginRequest,
    session_mgr = Depends(get_session_manager_dependency)
) -> LoginResponse:
    """
    用户登录
    
    - **email**: 用户邮箱（自动验证格式）
    - **password**: 用户密码（最少6位）
    - **remember_me**: 是否记住登录状态
    - **user_metadata**: 用户元数据
    """
    try:
        # 确保session管理器已初始化
        await session_mgr.initialize()
        
        # 执行登录
        result = await session_mgr.login(
            email=request.email,
            password=request.password,
            remember_me=request.remember_me,
            user_metadata=request.user_metadata
        )
        
        if result["success"]:
            # 构造用户信息响应
            user_info = UserInfoResponse(
                id=result["user"]["id"],
                email=result["user"]["email"],
                metadata=result["user"].get("user_metadata", {})
            )
            
            return LoginResponse(
                success=True,
                message=result["message"],
                user=user_info,
                session_expires_at=result.get("session_expires_at"),
                login_time=result.get("login_time")
            )
        else:
            return LoginResponse(
                success=False,
                message=result["message"]
            )
            
    except SessionManagerError as e:
        logger.error(f"Session manager error during login: {e.message}")
        raise HTTPException(status_code=400, detail=e.message)
    except Exception as e:
        logger.error(f"Unexpected error during login: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/logout", response_model=LogoutResponse, summary="用户登出")
async def logout(
    session_mgr = Depends(get_session_manager_dependency)
) -> LogoutResponse:
    """
    用户登出
    
    清理所有session数据和加密文件
    """
    try:
        result = await session_mgr.logout()
        
        return LogoutResponse(
            success=result["success"],
            message=result["message"]
        )
        
    except SessionManagerError as e:
        logger.error(f"Session manager error during logout: {e.message}")
        raise HTTPException(status_code=400, detail=e.message)
    except Exception as e:
        logger.error(f"Unexpected error during logout: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/refresh", response_model=RefreshTokenResponse, summary="刷新令牌")
async def refresh_token(
    request: RefreshTokenRequest,
    session_mgr = Depends(get_session_manager_dependency)
) -> RefreshTokenResponse:
    """
    刷新访问令牌
    
    - **refresh_token**: 刷新令牌（自动验证长度）
    """
    try:
        # 注意：这里需要扩展session_manager来支持外部refresh_token
        # 目前的实现使用内部存储的refresh_token
        result = await session_mgr.refresh_session()
        
        return RefreshTokenResponse(
            success=result["success"],
            message=result["message"],
            expires_at=result.get("expires_at")
        )
        
    except SessionExpiredError as e:
        logger.warning(f"Session expired during refresh: {e.message}")
        raise HTTPException(status_code=401, detail="Session expired")
    except SessionManagerError as e:
        logger.error(f"Session manager error during refresh: {e.message}")
        raise HTTPException(status_code=400, detail=e.message)
    except Exception as e:
        logger.error(f"Unexpected error during refresh: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/session", response_model=SessionInfoResponse, summary="获取Session信息")
async def get_session_info(
    session_mgr = Depends(get_session_manager_dependency)
) -> SessionInfoResponse:
    """
    获取当前session详细信息
    
    包括登录状态、用户信息、过期时间等
    """
    try:
        info = session_mgr.get_session_info()
        
        return SessionInfoResponse(
            logged_in=info["logged_in"],
            state=info["state"],
            user_email=info.get("user_email"),
            user_id=info.get("user_id"),
            login_time=info.get("login_time"),
            expires_at=info.get("expires_at"),
            last_activity=info.get("last_activity"),
            is_valid=info["is_valid"],
            time_until_expiry=info["time_until_expiry"],
            session_metadata=info.get("session_metadata", {})
        )
        
    except Exception as e:
        logger.error(f"Error getting session info: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/user", response_model=UserInfoResponse, summary="获取当前用户信息")
async def get_current_user(
    session_mgr = Depends(get_session_manager_dependency)
) -> UserInfoResponse:
    """
    获取当前登录用户的信息
    
    需要有效的session
    """
    try:
        if not session_mgr.is_logged_in():
            raise HTTPException(status_code=401, detail="Not logged in")
        
        user = session_mgr.get_current_user()
        if not user:
            raise HTTPException(status_code=401, detail="User session not found")
        
        return UserInfoResponse(
            id=user["id"],
            email=user["email"],
            metadata=user.get("metadata", {})
        )
        
    except HTTPException:
        raise
    except SessionNotFoundError as e:
        logger.warning(f"User session not found: {e.message}")
        raise HTTPException(status_code=401, detail="User session not found")
    except Exception as e:
        logger.error(f"Error getting current user: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/validate", summary="验证Session有效性")
async def validate_session(
    session_mgr = Depends(get_session_manager_dependency)
) -> JSONResponse:
    """
    验证当前session是否有效
    
    如果即将过期会自动刷新
    """
    try:
        is_valid = await session_mgr.ensure_valid_session()
        
        if is_valid:
            return JSONResponse(
                status_code=200,
                content={
                    "valid": True,
                    "message": "Session is valid",
                    "time_until_expiry": session_mgr.get_session_info()["time_until_expiry"]
                }
            )
        else:
            return JSONResponse(
                status_code=401,
                content={
                    "valid": False,
                    "message": "Session is invalid or expired"
                }
            )
            
    except SessionExpiredError as e:
        logger.info(f"Session validation failed - expired: {e.message}")
        return JSONResponse(
            status_code=401,
            content={
                "valid": False,
                "message": "Session expired",
                "details": e.details
            }
        )
    except Exception as e:
        logger.error(f"Error validating session: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/health", summary="认证服务健康检查")
async def auth_health_check() -> JSONResponse:
    """
    认证服务健康检查
    
    检查session管理器和相关服务状态
    """
    try:
        session_mgr = get_session_manager()
        
        # 基本健康检查
        health_status = {
            "status": "healthy",
            "session_manager": "initialized" if session_mgr._initialized else "not_initialized",
            "auto_refresh": session_mgr.auto_refresh,
            "multi_user": session_mgr.multi_user,
            "active_sessions": len(session_mgr._sessions),
            "current_session": session_mgr.is_logged_in()
        }
        
        return JSONResponse(
            status_code=200,
            content=health_status
        )
        
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return JSONResponse(
            status_code=503,
            content={
                "status": "unhealthy",
                "error": str(e)
            }
        )
