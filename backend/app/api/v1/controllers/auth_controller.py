"""
认证控制器

提供Supabase认证相关的API端点
"""
from typing import Dict, Any
from fastapi import APIRouter, HTTPException, status
from pydantic import BaseModel, EmailStr
from loguru import logger

from backend.app.services.supabase_client import auth_service


# 请求模型
class SignUpRequest(BaseModel):
    """用户注册请求模型"""
    email: EmailStr
    password: str
    full_name: str = None
    phone: str = None


class SignInRequest(BaseModel):
    """用户登录请求模型"""
    email: EmailStr
    password: str


class RefreshTokenRequest(BaseModel):
    """刷新令牌请求模型"""
    refresh_token: str


class ResetPasswordRequest(BaseModel):
    """重置密码请求模型"""
    email: EmailStr


class UpdateUserRequest(BaseModel):
    """更新用户信息请求模型"""
    full_name: str = None
    phone: str = None
    email: EmailStr = None


# 响应模型
class AuthResponse(BaseModel):
    """认证响应模型"""
    success: bool
    message: str
    user: Dict[str, Any] = None
    session: Dict[str, Any] = None
    access_token: str = None
    refresh_token: str = None


# 创建路由器
router = APIRouter(prefix="/auth", tags=["Authentication"])


@router.post("/signup", response_model=AuthResponse, summary="用户注册")
async def sign_up(request: SignUpRequest):
    """
    用户注册
    
    - **email**: 用户邮箱地址
    - **password**: 用户密码
    - **full_name**: 用户全名（可选）
    - **phone**: 用户电话（可选）
    """
    try:
        # 准备用户数据
        user_data = {}
        if request.full_name:
            user_data["full_name"] = request.full_name
        if request.phone:
            user_data["phone"] = request.phone
        
        # 调用认证服务
        result = await auth_service.sign_up_with_email(
            email=request.email,
            password=request.password,
            **user_data
        )
        
        if result["success"]:
            return AuthResponse(**result)
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result["message"]
            )
            
    except Exception as e:
        logger.error(f"Error in sign_up endpoint: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


@router.post("/signin", response_model=AuthResponse, summary="用户登录")
async def sign_in(request: SignInRequest):
    """
    用户登录
    
    - **email**: 用户邮箱地址
    - **password**: 用户密码
    """
    try:
        result = await auth_service.sign_in_with_email(
            email=request.email,
            password=request.password
        )
        
        if result["success"]:
            return AuthResponse(**result)
        else:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail=result["message"]
            )
            
    except Exception as e:
        logger.error(f"Error in sign_in endpoint: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


@router.post("/signout", summary="用户登出")
async def sign_out():
    """
    用户登出
    """
    try:
        result = await auth_service.sign_out()
        
        if result["success"]:
            return {"success": True, "message": result["message"]}
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result["message"]
            )
            
    except Exception as e:
        logger.error(f"Error in sign_out endpoint: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


@router.get("/user", response_model=AuthResponse, summary="获取当前用户")
async def get_current_user():
    """
    获取当前认证用户的信息
    """
    try:
        result = await auth_service.get_current_user()
        
        if result["success"]:
            return AuthResponse(**result)
        else:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail=result["message"]
            )
            
    except Exception as e:
        logger.error(f"Error in get_current_user endpoint: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


@router.post("/refresh", response_model=AuthResponse, summary="刷新会话")
async def refresh_session(request: RefreshTokenRequest):
    """
    刷新用户会话
    
    - **refresh_token**: 刷新令牌
    """
    try:
        result = await auth_service.refresh_session(request.refresh_token)
        
        if result["success"]:
            return AuthResponse(**result)
        else:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail=result["message"]
            )
            
    except Exception as e:
        logger.error(f"Error in refresh_session endpoint: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


@router.post("/reset-password", summary="重置密码")
async def reset_password(request: ResetPasswordRequest):
    """
    发送密码重置邮件
    
    - **email**: 用户邮箱地址
    """
    try:
        result = await auth_service.reset_password(request.email)
        
        if result["success"]:
            return {"success": True, "message": result["message"]}
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result["message"]
            )
            
    except Exception as e:
        logger.error(f"Error in reset_password endpoint: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


@router.put("/user", response_model=AuthResponse, summary="更新用户信息")
async def update_user(request: UpdateUserRequest):
    """
    更新当前用户信息
    
    - **full_name**: 用户全名（可选）
    - **phone**: 用户电话（可选）
    - **email**: 用户邮箱（可选）
    """
    try:
        # 准备更新数据
        update_data = {}
        if request.full_name is not None:
            update_data["data"] = {"full_name": request.full_name}
        if request.phone is not None:
            if "data" not in update_data:
                update_data["data"] = {}
            update_data["data"]["phone"] = request.phone
        if request.email is not None:
            update_data["email"] = request.email
        
        result = await auth_service.update_user(update_data)
        
        if result["success"]:
            return AuthResponse(**result)
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result["message"]
            )
            
    except Exception as e:
        logger.error(f"Error in update_user endpoint: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


@router.get("/test", summary="测试Supabase连接")
async def test_connection():
    """
    测试Supabase连接状态
    """
    try:
        from backend.app.services.supabase_client import supabase_client
        
        is_connected = supabase_client.test_connection()
        
        return {
            "success": is_connected,
            "message": "Connection successful" if is_connected else "Connection failed",
            "supabase_url": supabase_client.url
        }
        
    except Exception as e:
        logger.error(f"Error in test_connection endpoint: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )
