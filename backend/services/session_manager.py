"""
Supabase Session管理器

提供session的创建、保持、刷新和清理功能
"""

import json
import time
from typing import Optional, Dict, Any
from pathlib import Path
from loguru import logger

from backend.services.supabase_client import get_auth_service, get_supabase_client


class SessionManager:
    """Session管理器"""
    
    def __init__(self, session_file: str = "session.json"):
        """
        初始化Session管理器
        
        Args:
            session_file: session存储文件路径
        """
        self.session_file = Path(session_file)
        self.auth_service = get_auth_service()
        self.supabase_client = get_supabase_client()
        self._current_session: Optional[Dict[str, Any]] = None
        
        # 加载已保存的session
        self._load_session()
    
    def _load_session(self) -> None:
        """从文件加载session"""
        try:
            if self.session_file.exists():
                with open(self.session_file, 'r', encoding='utf-8') as f:
                    session_data = json.load(f)
                    
                # 检查session是否过期
                if self._is_session_valid(session_data):
                    self._current_session = session_data
                    logger.info("Loaded valid session from file")
                else:
                    logger.info("Session expired, removing file")
                    self.session_file.unlink(missing_ok=True)
        except Exception as e:
            logger.error(f"Error loading session: {e}")
            self.session_file.unlink(missing_ok=True)
    
    def _save_session(self, session_data: Dict[str, Any]) -> None:
        """保存session到文件"""
        try:
            with open(self.session_file, 'w', encoding='utf-8') as f:
                json.dump(session_data, f, indent=2, ensure_ascii=False)
            logger.info("Session saved to file")
        except Exception as e:
            logger.error(f"Error saving session: {e}")
    
    def _is_session_valid(self, session_data: Dict[str, Any]) -> bool:
        """检查session是否有效"""
        try:
            expires_at = session_data.get('expires_at')
            if not expires_at:
                return False
            
            # 检查是否过期（提前5分钟刷新）
            current_time = int(time.time())
            expires_timestamp = int(expires_at)
            
            return current_time < (expires_timestamp - 300)  # 提前5分钟
        except Exception:
            return False
    
    async def login(self, email: str, password: str, remember_me: bool = True) -> Dict[str, Any]:
        """
        用户登录
        
        Args:
            email: 用户邮箱
            password: 用户密码
            remember_me: 是否记住登录状态
            
        Returns:
            登录结果
        """
        try:
            # 如果已有有效session，先登出清理
            if self._current_session:
                await self.logout()
            
            # 执行登录
            result = await self.auth_service.sign_in_with_email(email, password)
            
            if result["success"] and result.get("session"):
                session_data = {
                    "user": result["user"],
                    "session": result["session"],
                    "access_token": result["access_token"],
                    "refresh_token": result["refresh_token"],
                    "expires_at": result["session"]["expires_at"],
                    "login_time": int(time.time())
                }
                
                self._current_session = session_data
                
                # 如果选择记住登录状态，保存到文件
                if remember_me:
                    self._save_session(session_data)
                
                logger.info(f"User logged in successfully: {email}")
                return {
                    "success": True,
                    "message": "Login successful",
                    "user": result["user"],
                    "session_expires_at": result["session"]["expires_at"]
                }
            else:
                return result
                
        except Exception as e:
            logger.error(f"Login error: {e}")
            return {
                "success": False,
                "message": str(e)
            }
    
    async def logout(self) -> Dict[str, Any]:
        """
        用户登出
        
        Returns:
            登出结果
        """
        try:
            # 调用Supabase登出API
            result = await self.auth_service.sign_out()
            
            # 清理本地session
            self._current_session = None
            
            # 删除session文件
            self.session_file.unlink(missing_ok=True)
            
            logger.info("User logged out successfully")
            return result
            
        except Exception as e:
            logger.error(f"Logout error: {e}")
            return {
                "success": False,
                "message": str(e)
            }
    
    async def refresh_session(self) -> Dict[str, Any]:
        """
        刷新session
        
        Returns:
            刷新结果
        """
        try:
            if not self._current_session:
                return {
                    "success": False,
                    "message": "No active session to refresh"
                }
            
            refresh_token = self._current_session.get("refresh_token")
            if not refresh_token:
                return {
                    "success": False,
                    "message": "No refresh token available"
                }
            
            # 调用刷新API
            result = await self.auth_service.refresh_session(refresh_token)
            
            if result["success"]:
                # 更新session数据
                session_data = {
                    "user": self._current_session["user"],
                    "session": result["session"],
                    "access_token": result["access_token"],
                    "refresh_token": result["refresh_token"],
                    "expires_at": result["session"]["expires_at"],
                    "login_time": self._current_session["login_time"]
                }
                
                self._current_session = session_data
                self._save_session(session_data)
                
                logger.info("Session refreshed successfully")
                return {
                    "success": True,
                    "message": "Session refreshed",
                    "expires_at": result["session"]["expires_at"]
                }
            else:
                # 刷新失败，清理session
                await self.logout()
                return result
                
        except Exception as e:
            logger.error(f"Session refresh error: {e}")
            await self.logout()
            return {
                "success": False,
                "message": str(e)
            }
    
    async def ensure_valid_session(self) -> bool:
        """
        确保session有效，如果即将过期则自动刷新
        
        Returns:
            session是否有效
        """
        try:
            if not self._current_session:
                return False
            
            # 检查session是否即将过期
            if not self._is_session_valid(self._current_session):
                logger.info("Session expiring soon, attempting refresh...")
                refresh_result = await self.refresh_session()
                return refresh_result["success"]
            
            return True
            
        except Exception as e:
            logger.error(f"Error ensuring valid session: {e}")
            return False
    
    def get_current_user(self) -> Optional[Dict[str, Any]]:
        """
        获取当前用户信息
        
        Returns:
            用户信息或None
        """
        if self._current_session:
            return self._current_session.get("user")
        return None
    
    def get_access_token(self) -> Optional[str]:
        """
        获取访问令牌
        
        Returns:
            访问令牌或None
        """
        if self._current_session:
            return self._current_session.get("access_token")
        return None
    
    def is_logged_in(self) -> bool:
        """
        检查是否已登录
        
        Returns:
            是否已登录
        """
        return self._current_session is not None and self._is_session_valid(self._current_session)
    
    def get_session_info(self) -> Dict[str, Any]:
        """
        获取session信息
        
        Returns:
            session信息
        """
        if not self._current_session:
            return {
                "logged_in": False,
                "message": "No active session"
            }
        
        user = self._current_session.get("user", {})
        expires_at = self._current_session.get("expires_at")
        login_time = self._current_session.get("login_time")
        
        return {
            "logged_in": True,
            "user_email": user.get("email"),
            "user_id": user.get("id"),
            "login_time": time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(login_time)) if login_time else None,
            "expires_at": time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(int(expires_at))) if expires_at else None,
            "is_valid": self._is_session_valid(self._current_session)
        }


# 创建全局session管理器实例
session_manager = SessionManager()


def get_session_manager() -> SessionManager:
    """
    获取session管理器实例
    
    Returns:
        SessionManager: session管理器实例
    """
    return session_manager
