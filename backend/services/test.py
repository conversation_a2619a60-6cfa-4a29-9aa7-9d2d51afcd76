import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from backend.services.supabase_auth_service import get_auth_service
import asyncio

async def test_supabase_connection():
    """测试Supabase连接（使用session管理）"""
    print("\n=== 测试Supabase连接（使用Session管理） ===")


    auth_service = get_auth_service()

    await auth_service.get_current_user

    # session_mgr = get_session_manager()
    #
    # # 检查是否已登录
    # if session_mgr.is_logged_in():
    #     print("✅ 已有有效登录状态")
    #     user = session_mgr.get_current_user()
    #     print(f"   当前用户: {user['email']}")
    #
    #     # 确保session有效
    #     is_valid = await session_mgr.ensure_valid_session()
    #     if is_valid:
    #         print("✅ Session有效")
    #     else:
    #         print("❌ Session无效，需要重新登录")
    # else:
    #     print("未登录，执行登录...")
    #     response = await session_mgr.login("<EMAIL>", "heygo01!", remember_me=True)
    #     print(f"登录结果: {response}")
    #
    # # 显示session信息
    # info = session_mgr.get_session_info()
    # print(f"\nSession信息: {info}")


if __name__ == "__main__":
    asyncio.run(test_supabase_connection())