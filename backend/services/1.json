{'success': True, 'user': {'id': '80b8fc3e-86da-4de7-b278-84f18027b2bc', 'app_metadata': {'provider': 'email', 'providers': ['email']}, 'user_metadata': {'email_verified': True}, 'aud': 'authenticated', 'confirmation_sent_at': None, 'recovery_sent_at': None, 'email_change_sent_at': None, 'new_email': None, 'new_phone': None, 'invited_at': None, 'action_link': None, 'email': '<EMAIL>', 'phone': '', 'created_at': datetime.datetime(2025, 8, 21, 9, 4, 55, 352693, tzinfo=TzInfo(UTC)), 'confirmed_at': datetime.datetime(2025, 8, 21, 9, 4, 55, 383481, tzinfo=TzInfo(UTC)), 'email_confirmed_at': datetime.datetime(2025, 8, 21, 9, 4, 55, 383481, tzinfo=TzInfo(UTC)), 'phone_confirmed_at': None, 'last_sign_in_at': datetime.datetime(2025, 8, 21, 11, 42, 50, 955714, tzinfo=TzInfo(UTC)), 'role': 'authenticated', 'updated_at': datetime.datetime(2025, 8, 21, 11, 42, 50, 958578, tzinfo=TzInfo(UTC)), 'identities': [{'id': '80b8fc3e-86da-4de7-b278-84f18027b2bc', 'identity_id': '773c7b1e-2901-4018-b692-a37b86ebc1d9', 'user_id': '80b8fc3e-86da-4de7-b278-84f18027b2bc', 'identity_data': {'email': '<EMAIL>', 'email_verified': False, 'phone_verified': False, 'sub': '80b8fc3e-86da-4de7-b278-84f18027b2bc'}, 'provider': 'email', 'created_at': datetime.datetime(2025, 8, 21, 9, 4, 55, 363459, tzinfo=TzInfo(UTC)), 'last_sign_in_at': datetime.datetime(2025, 8, 21, 9, 4, 55, 363370, tzinfo=TzInfo(UTC)), 'updated_at': datetime.datetime(2025, 8, 21, 9, 4, 55, 363459, tzinfo=TzInfo(UTC))}], 'is_anonymous': False, 'factors': None}, 'session': {'provider_token': None, 'provider_refresh_token': None, 'access_token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.vAb0Ur0qCOER9CJUKsy27aTMMjgvF-wgjRHASq6GwVo', 'refresh_token': '2qp7zfg776fx', 'expires_in': 3600, 'expires_at': **********, 'token_type': 'bearer', 'user': {'id': '80b8fc3e-86da-4de7-b278-84f18027b2bc', 'app_metadata': {'provider': 'email', 'providers': ['email']}, 'user_metadata': {'email_verified': True}, 'aud': 'authenticated', 'confirmation_sent_at': None, 'recovery_sent_at': None, 'email_change_sent_at': None, 'new_email': None, 'new_phone': None, 'invited_at': None, 'action_link': None, 'email': '<EMAIL>', 'phone': '', 'created_at': datetime.datetime(2025, 8, 21, 9, 4, 55, 352693, tzinfo=TzInfo(UTC)), 'confirmed_at': datetime.datetime(2025, 8, 21, 9, 4, 55, 383481, tzinfo=TzInfo(UTC)), 'email_confirmed_at': datetime.datetime(2025, 8, 21, 9, 4, 55, 383481, tzinfo=TzInfo(UTC)), 'phone_confirmed_at': None, 'last_sign_in_at': datetime.datetime(2025, 8, 21, 11, 42, 50, 955714, tzinfo=TzInfo(UTC)), 'role': 'authenticated', 'updated_at': datetime.datetime(2025, 8, 21, 11, 42, 50, 958578, tzinfo=TzInfo(UTC)), 'identities': [{'id': '80b8fc3e-86da-4de7-b278-84f18027b2bc', 'identity_id': '773c7b1e-2901-4018-b692-a37b86ebc1d9', 'user_id': '80b8fc3e-86da-4de7-b278-84f18027b2bc', 'identity_data': {'email': '<EMAIL>', 'email_verified': False, 'phone_verified': False, 'sub': '80b8fc3e-86da-4de7-b278-84f18027b2bc'}, 'provider': 'email', 'created_at': datetime.datetime(2025, 8, 21, 9, 4, 55, 363459, tzinfo=TzInfo(UTC)), 'last_sign_in_at': datetime.datetime(2025, 8, 21, 9, 4, 55, 363370, tzinfo=TzInfo(UTC)), 'updated_at': datetime.datetime(2025, 8, 21, 9, 4, 55, 363459, tzinfo=TzInfo(UTC))}], 'is_anonymous': False, 'factors': None}}, 'access_token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.vAb0Ur0qCOER9CJUKsy27aTMMjgvF-wgjRHASq6GwVo', 'refresh_token': '2qp7zfg776fx', 'message': 'Login successful'}
