"""
Schemas使用示例

展示如何使用分离后的Pydantic schemas
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from backend.app.services.supabase_session_manager import get_session_manager
from backend.app.api.v1.schemas import (
    # 核心数据模型
    SessionData,
    SessionState,
    SessionEvent,
    # API请求/响应模型
    LoginRequest,
    LoginResponse,
    UserInfoResponse,
    SessionInfoResponse,
    # 数据库模型
    DatabaseQueryRequest,
    DatabaseInsertRequest,
    DatabaseResponse,
    # 异常类
    SessionManagerError,
    SessionExpiredError,
    SessionValidationError
)
from pydantic import ValidationError


async def demo_request_response_models():
    """演示API请求/响应模型"""
    print("🔄 API请求/响应模型演示")
    print("=" * 40)
    
    # 1. 创建登录请求
    print("1️⃣ 创建登录请求:")
    try:
        login_req = LoginRequest(
            email="<EMAIL>",
            password="password123",
            remember_me=True,
            user_metadata={"source": "demo", "version": "1.0"}
        )
        print(f"   ✅ 登录请求创建成功")
        print(f"   邮箱: {login_req.email}")
        print(f"   记住登录: {login_req.remember_me}")
        print(f"   元数据: {login_req.user_metadata}")
        
        # JSON序列化
        json_data = login_req.model_dump_json(indent=2)
        print(f"   JSON长度: {len(json_data)} 字符")
        
    except ValidationError as e:
        print(f"   ❌ 验证失败: {e}")
    
    # 2. 创建用户信息响应
    print("\n2️⃣ 创建用户信息响应:")
    try:
        user_info = UserInfoResponse(
            id="123e4567-e89b-12d3-a456-426614174000",
            email="<EMAIL>",
            metadata={"role": "user", "created_at": "2023-12-21"}
        )
        print(f"   ✅ 用户信息响应创建成功")
        print(f"   用户ID: {user_info.id}")
        print(f"   邮箱: {user_info.email}")
        
    except ValidationError as e:
        print(f"   ❌ 验证失败: {e}")
    
    # 3. 创建登录响应
    print("\n3️⃣ 创建登录响应:")
    try:
        login_resp = LoginResponse(
            success=True,
            message="Login successful",
            user=user_info,
            session_expires_at=1735689600,
            login_time=1703153600
        )
        print(f"   ✅ 登录响应创建成功")
        print(f"   成功: {login_resp.success}")
        print(f"   消息: {login_resp.message}")
        print(f"   用户: {login_resp.user.email if login_resp.user else None}")
        
    except ValidationError as e:
        print(f"   ❌ 验证失败: {e}")


async def demo_database_models():
    """演示数据库相关模型"""
    print("\n🗄️ 数据库模型演示")
    print("=" * 30)
    
    # 1. 数据库查询请求
    print("1️⃣ 数据库查询请求:")
    try:
        query_req = DatabaseQueryRequest(
            table="user_profiles",
            columns="id, email, full_name",
            filters={"status": "active"},
            limit=10
        )
        print(f"   ✅ 查询请求创建成功")
        print(f"   表名: {query_req.table}")
        print(f"   列: {query_req.columns}")
        print(f"   过滤条件: {query_req.filters}")
        print(f"   限制: {query_req.limit}")
        
    except ValidationError as e:
        print(f"   ❌ 验证失败: {e}")
    
    # 2. 数据库插入请求
    print("\n2️⃣ 数据库插入请求:")
    try:
        insert_req = DatabaseInsertRequest(
            table="user_profiles",
            data={
                "email": "<EMAIL>",
                "full_name": "New User",
                "status": "active"
            }
        )
        print(f"   ✅ 插入请求创建成功")
        print(f"   表名: {insert_req.table}")
        print(f"   数据: {insert_req.data}")
        
    except ValidationError as e:
        print(f"   ❌ 验证失败: {e}")
    
    # 3. 数据库响应
    print("\n3️⃣ 数据库响应:")
    try:
        db_resp = DatabaseResponse(
            success=True,
            message="Query executed successfully",
            data=[
                {"id": 1, "email": "<EMAIL>", "full_name": "User One"},
                {"id": 2, "email": "<EMAIL>", "full_name": "User Two"}
            ],
            count=2
        )
        print(f"   ✅ 数据库响应创建成功")
        print(f"   成功: {db_resp.success}")
        print(f"   消息: {db_resp.message}")
        print(f"   数据条数: {db_resp.count}")
        print(f"   数据: {len(db_resp.data)} 条记录")
        
    except ValidationError as e:
        print(f"   ❌ 验证失败: {e}")


async def demo_session_data_validation():
    """演示SessionData验证"""
    print("\n🔍 SessionData验证演示")
    print("=" * 40)
    
    # 1. 正确的数据
    print("1️⃣ 正确数据验证:")
    try:
        session_data = SessionData(
            user_id="123e4567-e89b-12d3-a456-426614174000",
            email="<EMAIL>",  # 会自动转小写
            access_token="  eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9  ",  # 会自动去空格
            refresh_token="refresh_token_example_123456",
            expires_at=1735689600,
            login_time=1703153600,
            last_activity=1703153600,
            user_metadata={"role": "user"},
            session_metadata={"device": "web"}
        )
        print(f"   ✅ SessionData创建成功")
        print(f"   邮箱（自动小写）: {session_data.email}")
        print(f"   令牌（自动去空格）: {session_data.access_token[:20]}...")
        print(f"   是否过期: {session_data.is_expired()}")
        print(f"   距离过期: {session_data.time_until_expiry()}秒")
        print(f"   会话持续: {session_data.get_session_duration()}秒")
        
        # 安全序列化
        safe_dict = session_data.to_safe_dict()
        print(f"   安全令牌: {safe_dict['access_token']}")
        
    except ValidationError as e:
        print(f"   ❌ 验证失败: {e}")
    
    # 2. 错误的数据
    print("\n2️⃣ 错误数据验证:")
    invalid_examples = [
        {
            "name": "无效邮箱",
            "data": {
                "user_id": "123",
                "email": "invalid-email",
                "access_token": "token123456",
                "refresh_token": "refresh123456",
                "expires_at": 1735689600,
                "login_time": 1703153600,
                "last_activity": 1703153600,
            }
        },
        {
            "name": "令牌太短",
            "data": {
                "user_id": "123",
                "email": "<EMAIL>",
                "access_token": "short",  # 太短
                "refresh_token": "short",  # 太短
                "expires_at": 1735689600,
                "login_time": 1703153600,
                "last_activity": 1703153600,
            }
        },
        {
            "name": "负数时间戳",
            "data": {
                "user_id": "123",
                "email": "<EMAIL>",
                "access_token": "token123456",
                "refresh_token": "refresh123456",
                "expires_at": -1,  # 负数
                "login_time": -1,  # 负数
                "last_activity": -1,  # 负数
            }
        }
    ]
    
    for example in invalid_examples:
        print(f"\n   测试: {example['name']}")
        try:
            SessionData.model_validate(example['data'])
            print("     ❌ 应该验证失败但成功了")
        except ValidationError as e:
            print("     ✅ 正确捕获验证错误:")
            for error in e.errors()[:2]:  # 只显示前2个错误
                field = " -> ".join(str(loc) for loc in error['loc'])
                print(f"       字段 '{field}': {error['msg']}")


async def demo_exception_handling():
    """演示异常处理"""
    print("\n🛡️ 异常处理演示")
    print("=" * 30)
    
    # 1. SessionExpiredError
    print("1️⃣ Session过期异常:")
    try:
        # 创建一个过期的session
        expired_session = SessionData(
            user_id="expired-user",
            email="<EMAIL>",
            access_token="expired_token_123456",
            refresh_token="expired_refresh_123456",
            expires_at=int(asyncio.get_event_loop().time()) - 3600,  # 1小时前过期
            login_time=int(asyncio.get_event_loop().time()) - 7200,  # 2小时前登录
            last_activity=int(asyncio.get_event_loop().time()) - 3600,
        )
        
        if expired_session.is_expired():
            raise SessionExpiredError(expired_session)
            
    except SessionExpiredError as e:
        print(f"   ✅ 捕获过期异常: {e.message}")
        print(f"   异常详情: {e.details}")
    
    # 2. ValidationError转换为SessionValidationError
    print("\n2️⃣ 验证异常:")
    try:
        invalid_data = {
            "user_id": "",
            "email": "not-an-email",
            "access_token": "short",
            "refresh_token": "short",
            "expires_at": -1,
            "login_time": 0,
            "last_activity": 0,
        }
        SessionData.model_validate(invalid_data)
    except ValidationError as e:
        validation_errors = []
        for error in e.errors():
            validation_errors.append({
                "field": " -> ".join(str(loc) for loc in error['loc']),
                "error": error['msg'],
                "value": error.get('input')
            })
        
        session_validation_error = SessionValidationError(validation_errors)
        print(f"   ✅ 捕获验证异常: {session_validation_error.message}")
        print("   验证错误详情:")
        for error in session_validation_error.details['validation_errors'][:3]:  # 显示前3个
            print(f"     - {error['field']}: {error['error']}")


async def demo_real_integration():
    """演示真实集成使用"""
    print("\n🌍 真实集成演示")
    print("=" * 30)
    
    session_mgr = get_session_manager()
    await session_mgr.initialize()
    
    print("1️⃣ 使用schemas进行登录:")
    
    # 创建登录请求（使用schema验证）
    try:
        login_request = LoginRequest(
            email="<EMAIL>",
            password="heygo01!",
            remember_me=True,
            user_metadata={"source": "schemas_demo"}
        )
        print(f"   ✅ 登录请求验证通过")
        
        # 执行登录
        result = await session_mgr.login(
            email=login_request.email,
            password=login_request.password,
            remember_me=login_request.remember_me,
            user_metadata=login_request.user_metadata
        )
        
        if result["success"]:
            # 创建响应模型
            user_info = UserInfoResponse(
                id=result["user"]["id"],
                email=result["user"]["email"],
                metadata=result["user"].get("user_metadata", {})
            )
            
            login_response = LoginResponse(
                success=True,
                message=result["message"],
                user=user_info,
                session_expires_at=result.get("session_expires_at"),
                login_time=result.get("login_time")
            )
            
            print(f"   ✅ 登录成功，响应模型创建完成")
            print(f"   用户: {login_response.user.email}")
            print(f"   过期时间: {login_response.session_expires_at}")
            
        else:
            print(f"   ❌ 登录失败: {result['message']}")
            
    except ValidationError as e:
        print(f"   ❌ 请求验证失败: {e}")
    except Exception as e:
        print(f"   ❌ 登录过程出错: {e}")


async def main():
    """主演示函数"""
    print("🚀 Schemas分离架构演示")
    print("=" * 50)
    
    try:
        # 1. API请求/响应模型演示
        await demo_request_response_models()
        
        # 2. 数据库模型演示
        await demo_database_models()
        
        # 3. SessionData验证演示
        await demo_session_data_validation()
        
        # 4. 异常处理演示
        await demo_exception_handling()
        
        # 5. 真实集成演示
        await demo_real_integration()
        
        print("\n🎉 所有演示完成!")
        
        print("\n💡 Schemas分离的优势:")
        print("✅ 清晰的架构分层")
        print("✅ 统一的数据验证")
        print("✅ 类型安全的API")
        print("✅ 自动生成的文档")
        print("✅ 易于测试和维护")
        print("✅ 复用性强")
        
    except Exception as e:
        print(f"❌ 演示过程中出错: {e}")


if __name__ == "__main__":
    asyncio.run(main())
