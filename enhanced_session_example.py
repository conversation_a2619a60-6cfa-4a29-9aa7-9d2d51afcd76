"""
增强版Session管理器使用示例

展示所有优化功能：
- 🔒 安全：加密存储
- ⚡ 性能：异步操作
- 🎯 功能：事件系统、多用户
- 🏗️ 架构：状态管理
- 👥 用户体验：智能刷新
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from backend.services.enhanced_session_manager import (
    get_enhanced_session_manager, 
    SessionEvent, 
    SessionState
)


async def demo_event_system():
    """演示事件系统"""
    print("🎭 事件系统演示")
    print("=" * 30)
    
    session_mgr = get_enhanced_session_manager()
    
    # 注册事件处理器
    async def on_login_success(event, data):
        print(f"🎉 登录成功事件: {data['user']['email']}")
    
    async def on_logout(event, data):
        print(f"👋 登出事件: {data['email']}")
    
    def on_error(event, data):
        print(f"❌ 错误事件: {data['error']} (上下文: {data['context']})")
    
    # 注册事件监听器
    session_mgr.on(SessionEvent.LOGIN_SUCCESS, on_login_success)
    session_mgr.on(SessionEvent.LOGOUT, on_logout)
    session_mgr.on(SessionEvent.ERROR_OCCURRED, on_error)
    
    print("✅ 事件处理器已注册")
    return session_mgr


async def demo_enhanced_login():
    """演示增强登录功能"""
    print("\n🔐 增强登录功能演示")
    print("=" * 40)
    
    session_mgr = await demo_event_system()
    
    # 确保初始化
    await session_mgr.initialize()
    
    # 检查当前状态
    print(f"当前状态: {session_mgr._state.value}")
    
    # 登录（带用户元数据）
    print("\n1️⃣ 执行登录...")
    result = await session_mgr.login(
        email="<EMAIL>",
        password="heygo01!",
        remember_me=True,
        user_metadata={
            "app_version": "1.0.0",
            "device_type": "python_client",
            "login_source": "demo"
        }
    )
    
    if result["success"]:
        print("✅ 登录成功!")
        print(f"   用户: {result['user']['email']}")
        print(f"   登录时间: {result['login_time']}")
        
        # 获取详细session信息
        print("\n2️⃣ Session详细信息:")
        info = session_mgr.get_session_info()
        print(f"   状态: {info['state']}")
        print(f"   过期时间: {info['expires_at']}")
        print(f"   距离过期: {info['time_until_expiry']}秒")
        print(f"   元数据: {info['session_metadata']}")
        
        return True
    else:
        print(f"❌ 登录失败: {result['message']}")
        return False


async def demo_smart_refresh():
    """演示智能刷新功能"""
    print("\n🔄 智能刷新功能演示")
    print("=" * 40)
    
    session_mgr = get_enhanced_session_manager()
    
    if not session_mgr.is_logged_in():
        print("⚠️ 用户未登录，跳过刷新演示")
        return
    
    print("1️⃣ 当前session状态:")
    info = session_mgr.get_session_info()
    print(f"   有效: {info['is_valid']}")
    print(f"   距离过期: {info['time_until_expiry']}秒")
    
    # 模拟接近过期的情况（实际中由自动刷新处理）
    print("\n2️⃣ 自动刷新机制:")
    print(f"   自动刷新: {'启用' if session_mgr.auto_refresh else '禁用'}")
    print(f"   刷新缓冲时间: {session_mgr.refresh_buffer}秒")
    print("   ℹ️ 系统会在token过期前自动刷新")


async def demo_security_features():
    """演示安全功能"""
    print("\n🔒 安全功能演示")
    print("=" * 30)
    
    session_mgr = get_enhanced_session_manager()
    
    print("1️⃣ 加密存储:")
    print(f"   Session文件: {session_mgr.session_file}")
    print("   ✅ 所有敏感数据都经过加密存储")
    
    print("\n2️⃣ Token安全:")
    if session_mgr.is_logged_in():
        token = session_mgr.get_access_token()
        print(f"   访问令牌: {token[:20]}... (已截断显示)")
        print("   ✅ Token获取时自动更新活动时间")
    else:
        print("   ⚠️ 未登录，无法获取token")
    
    print("\n3️⃣ 状态保护:")
    print(f"   当前状态: {session_mgr._state.value}")
    print("   ✅ 使用状态机防止非法状态转换")


async def demo_multi_user_support():
    """演示多用户支持（需要启用multi_user模式）"""
    print("\n👥 多用户支持演示")
    print("=" * 30)
    
    # 创建支持多用户的session管理器
    multi_session_mgr = get_enhanced_session_manager()
    
    if not multi_session_mgr.multi_user:
        print("⚠️ 当前实例未启用多用户模式")
        print("   可以通过 EnhancedSessionManager(multi_user=True) 启用")
        return
    
    print("✅ 多用户模式已启用")
    print("   支持同时管理多个用户的session")
    print("   每个用户都有独立的加密存储")


async def demo_performance_features():
    """演示性能优化功能"""
    print("\n⚡ 性能优化演示")
    print("=" * 30)
    
    session_mgr = get_enhanced_session_manager()
    
    print("1️⃣ 异步操作:")
    print("   ✅ 所有I/O操作都是异步的")
    print("   ✅ 使用异步锁防止竞态条件")
    
    print("\n2️⃣ 智能缓存:")
    print(f"   当前缓存的sessions: {len(session_mgr._sessions)}")
    print("   ✅ 内存中缓存活跃session")
    
    print("\n3️⃣ 后台任务:")
    if session_mgr._refresh_task and not session_mgr._refresh_task.done():
        print("   ✅ 自动刷新任务正在运行")
    else:
        print("   ⚠️ 自动刷新任务未运行")


async def demo_error_handling():
    """演示错误处理"""
    print("\n🛡️ 错误处理演示")
    print("=" * 30)
    
    session_mgr = get_enhanced_session_manager()
    
    # 注册错误事件处理器
    error_count = 0
    
    def error_handler(event, data):
        nonlocal error_count
        error_count += 1
        print(f"   捕获到错误 #{error_count}: {data['error']}")
    
    session_mgr.on(SessionEvent.ERROR_OCCURRED, error_handler)
    
    print("1️⃣ 错误事件处理:")
    print("   ✅ 统一的错误事件系统")
    print("   ✅ 详细的错误上下文信息")
    
    # 模拟一个错误（尝试用错误密码登录）
    print("\n2️⃣ 模拟登录错误:")
    result = await session_mgr.login("<EMAIL>", "wrong_password")
    if not result["success"]:
        print(f"   预期的登录失败: {result['message']}")
    
    print(f"\n   总共捕获错误: {error_count} 个")


async def demo_cleanup():
    """演示资源清理"""
    print("\n🧹 资源清理演示")
    print("=" * 30)
    
    session_mgr = get_enhanced_session_manager()
    
    print("1️⃣ 登出清理:")
    if session_mgr.is_logged_in():
        result = await session_mgr.logout()
        if result["success"]:
            print("   ✅ 登出成功，资源已清理")
        else:
            print(f"   ❌ 登出失败: {result['message']}")
    else:
        print("   ℹ️ 用户未登录，无需清理")
    
    print("\n2️⃣ 任务清理:")
    if session_mgr._refresh_task:
        session_mgr._refresh_task.cancel()
        print("   ✅ 后台刷新任务已取消")
    
    print("\n3️⃣ 文件清理:")
    if session_mgr.session_file.exists():
        print(f"   Session文件存在: {session_mgr.session_file}")
        print("   ℹ️ 文件在登出时会自动清理")
    else:
        print("   ✅ Session文件已清理")


async def main():
    """主演示函数"""
    print("🚀 增强版Session管理器完整演示")
    print("=" * 50)
    
    try:
        # 1. 演示增强登录
        login_success = await demo_enhanced_login()
        
        if login_success:
            # 2. 演示智能刷新
            await demo_smart_refresh()
            
            # 3. 演示安全功能
            await demo_security_features()
            
            # 4. 演示性能功能
            await demo_performance_features()
        
        # 5. 演示多用户支持
        await demo_multi_user_support()
        
        # 6. 演示错误处理
        await demo_error_handling()
        
        # 7. 演示资源清理
        await demo_cleanup()
        
        print("\n🎉 所有演示完成!")
        
        print("\n💡 优化总结:")
        print("✅ 安全性: 加密存储、防token泄露")
        print("✅ 性能: 异步操作、智能缓存")
        print("✅ 功能: 多用户、事件系统、自动刷新")
        print("✅ 架构: 依赖注入、状态机、错误处理")
        print("✅ 用户体验: 智能刷新、详细状态信息")
        
    except Exception as e:
        print(f"❌ 演示过程中出错: {e}")


if __name__ == "__main__":
    # 需要先安装cryptography库
    try:
        import cryptography
    except ImportError:
        print("❌ 需要安装cryptography库:")
        print("   uv add cryptography")
        exit(1)
    
    asyncio.run(main())
